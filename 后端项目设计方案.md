# 电商平台后端项目设计方案

## 项目概述

基于前端提供的接口文档和数据库设计，使用 Java Spring Boot 构建高性能、企业级的电商平台后端服务。

## 技术栈选择

### 核心框架
- **Web框架**: Spring Boot 3.2+ - 企业级Java Web框架
- **数据库**: MySQL 8.0+ - 关系型数据库
- **ORM**: MyBatis-Plus 3.5+ - 增强版MyBatis，支持代码生成
- **连接池**: HikariCP - 高性能数据库连接池

### 认证与安全
- **JWT**: jjwt - JWT token生成和验证
- **密码加密**: BCrypt - Spring Security内置密码加密
- **安全框架**: Spring Security 6+ - 认证授权框架
- **CORS**: Spring Web CORS - 跨域请求处理

### 序列化与配置
- **JSON处理**: Jackson - JSON序列化/反序列化
- **配置管理**: Spring Boot Configuration - 配置文件管理
- **验证**: Hibernate Validator - 数据验证框架

### 日志与监控
- **日志**: Logback + SLF4J - 日志框架
- **监控**: Spring Boot Actuator + Micrometer - 应用监控
- **API文档**: Swagger 3 (OpenAPI) - API文档生成

### 文件处理
- **文件上传**: Spring Web MultipartFile - 文件上传处理
- **图片处理**: ImageIO + Thumbnailator - 图片压缩和处理
- **文件存储**: 本地存储 + 阿里云OSS/腾讯云COS

### 缓存与队列
- **缓存**: Redis + Spring Cache - 分布式缓存
- **消息队列**: RabbitMQ/RocketMQ - 异步消息处理


## 项目架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │────│   API Gateway   │────│   后端服务      │
│   (React/Vue)   │    │   (Nginx)       │    │ (Spring Boot)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Redis缓存     │────│   MySQL数据库   │
                       │                 │    │                 │
                       └─────────────────┘    └─────────────────┘
```

### 分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Controllers │ │   Filters   │ │ Interceptors│           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                     Business Layer                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  Services   │ │    DTOs     │ │ Validators  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                   Data Access Layer                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Mappers   │ │   Entities  │ │ Migrations  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                  Infrastructure Layer                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  Database   │ │    Cache    │ │ File Storage│           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 项目目录结构

```
ecommerce-backend/
├── pom.xml                       # Maven项目配置文件
├── .env.example                  # 环境变量示例
├── .gitignore                    # Git忽略文件
├── README.md                     # 项目说明
├── docker-compose.yml            # Docker编排文件
├── Dockerfile                    # Docker镜像构建
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── ecommerce/
│   │   │           ├── EcommerceApplication.java  # Spring Boot启动类
│   │   │           ├── config/                    # 配置类
│   │   │           │   ├── DatabaseConfig.java    # 数据库配置
│   │   │           │   ├── SecurityConfig.java    # 安全配置
│   │   │           │   ├── RedisConfig.java       # Redis配置
│   │   │           │   └── SwaggerConfig.java     # Swagger配置
│   │   │           ├── controller/               # 控制器层
│   │   │           │   ├── AuthController.java   # 认证控制器
│   │   │           │   ├── UserController.java   # 用户控制器
│   │   │           │   ├── ProductController.java # 商品控制器
│   │   │           │   ├── OrderController.java  # 订单控制器
│   │   │           │   ├── AdminController.java  # 管理员控制器
│   │   │           │   └── UploadController.java # 文件上传控制器
│   │   │           ├── service/                  # 业务逻辑层
│   │   │           │   ├── AuthService.java      # 认证服务
│   │   │           │   ├── UserService.java      # 用户服务
│   │   │           │   ├── ProductService.java   # 商品服务
│   │   │           │   ├── OrderService.java     # 订单服务
│   │   │           │   ├── PaymentService.java   # 支付服务
│   │   │           │   └── UploadService.java    # 文件上传服务
│   │   │           ├── mapper/                   # MyBatis映射器
│   │   │           │   ├── UserMapper.java       # 用户数据访问
│   │   │           │   ├── ProductMapper.java    # 商品数据访问
│   │   │           │   ├── OrderMapper.java      # 订单数据访问
│   │   │           │   └── CategoryMapper.java   # 分类数据访问
│   │   │           ├── entity/                   # 实体类
│   │   │           │   ├── User.java             # 用户实体
│   │   │           │   ├── Product.java          # 商品实体
│   │   │           │   ├── Order.java            # 订单实体
│   │   │           │   ├── Category.java         # 分类实体
│   │   │           │   └── Payment.java          # 支付实体
│   │   │           ├── dto/                      # 数据传输对象
│   │   │           │   ├── request/              # 请求DTO
│   │   │           │   │   ├── LoginRequest.java
│   │   │           │   │   ├── RegisterRequest.java
│   │   │           │   │   └── CreateOrderRequest.java
│   │   │           │   ├── response/             # 响应DTO
│   │   │           │   │   ├── LoginResponse.java
│   │   │           │   │   ├── UserResponse.java
│   │   │           │   │   └── ProductResponse.java
│   │   │           │   └── common/               # 通用DTO
│   │   │           │       ├── ApiResponse.java
│   │   │           │       └── PageResponse.java
│   │   │           ├── security/                 # 安全相关
│   │   │           │   ├── JwtAuthenticationFilter.java
│   │   │           │   ├── JwtTokenProvider.java
│   │   │           │   └── UserDetailsServiceImpl.java
│   │   │           ├── utils/                    # 工具类
│   │   │           │   ├── JwtUtils.java         # JWT工具
│   │   │           │   ├── PasswordUtils.java    # 密码工具
│   │   │           │   ├── ValidationUtils.java  # 验证工具
│   │   │           │   ├── PageUtils.java        # 分页工具
│   │   │           │   └── FileUtils.java        # 文件工具
│   │   │           ├── exception/                # 异常处理
│   │   │           │   ├── GlobalExceptionHandler.java
│   │   │           │   ├── BusinessException.java
│   │   │           │   └── ErrorCode.java
│   │   │           └── common/                   # 通用组件
│   │   │               ├── constants/            # 常量定义
│   │   │               ├── enums/                # 枚举定义
│   │   │               └── interceptor/          # 拦截器
│   │   └── resources/
│   │       ├── application.yml           # 主配置文件
│   │       ├── application-dev.yml       # 开发环境配置
│   │       ├── application-prod.yml      # 生产环境配置
│   │       ├── mapper/                   # MyBatis XML映射文件
│   │       │   ├── UserMapper.xml
│   │       │   ├── ProductMapper.xml
│   │       │   └── OrderMapper.xml
│   │       ├── db/migration/             # 数据库迁移脚本
│   │       │   ├── V1__Create_users_table.sql
│   │       │   ├── V2__Create_products_table.sql
│   │       │   └── V3__Create_orders_table.sql
│   │       └── static/                   # 静态资源
│   └── test/
│       └── java/
│           └── com/
│               └── ecommerce/
│                   ├── controller/       # 控制器测试
│                   ├── service/          # 服务测试
│                   └── integration/      # 集成测试
├── docs/                                 # 文档
│   ├── api.md                            # API文档
│   ├── deployment.md                     # 部署文档
│   └── development.md                    # 开发文档
└── scripts/                              # 脚本文件
    ├── init_db.sh                        # 数据库初始化脚本
    ├── migrate.sh                        # 数据库迁移脚本
    └── deploy.sh                         # 部署脚本
```

## 核心模块设计

### 1. 认证授权模块 (Auth)
**功能**:
- 用户登录/注册
- JWT token生成和验证
- 刷新token机制
- 密码加密和验证

**主要组件**:
- `AuthController`: 处理认证相关HTTP请求
- `AuthService`: 认证业务逻辑
- `JwtAuthenticationFilter`: JWT验证过滤器
- `LoginRequest/Response`: 认证相关数据传输对象

### 2. 用户管理模块 (Users)
**功能**:
- 用户信息CRUD
- 用户地址管理
- 用户头像上传
- 用户统计信息

**主要组件**:
- `UserController`: 用户相关请求处理
- `UserService`: 用户业务逻辑
- `UserMapper`: 用户数据访问
- `UserResponse`: 用户相关DTO

### 3. 商品管理模块 (Products)
**功能**:
- 商品CRUD操作
- 商品分类管理
- 商品搜索和筛选
- 商品图片管理
- 商品评价系统
- 商品收藏功能

**主要组件**:
- `ProductController`: 商品请求处理
- `ProductService`: 商品业务逻辑
- `CategoryService`: 分类业务逻辑
- `ProductMapper`: 商品数据访问

### 4. 订单管理模块 (Orders)
**功能**:
- 订单创建和管理
- 订单状态流转
- 订单商品管理
- 订单地址管理
- 物流信息管理

**主要组件**:
- `OrderController`: 订单请求处理
- `OrderService`: 订单业务逻辑
- `ShippingService`: 物流业务逻辑
- `OrderMapper`: 订单数据访问

### 5. 支付管理模块 (Payments)
**功能**:
- 支付订单处理
- 支付状态管理
- 退款处理
- 支付方式配置

**主要组件**:
- `PaymentController`: 支付请求处理
- `PaymentService`: 支付业务逻辑
- `RefundService`: 退款业务逻辑
- `PaymentMapper`: 支付数据访问

### 6. 文件上传模块 (Uploads)
**功能**:
- 图片上传和处理
- 文件存储管理
- 图片压缩和格式转换
- 文件访问权限控制

**主要组件**:
- `UploadController`: 文件上传处理
- `UploadService`: 文件处理业务逻辑
- `FileStorageService`: 文件存储服务
- `ImageProcessor`: 图片处理工具

### 7. 管理员模块 (Admin)
**功能**:
- 数据统计和分析
- 用户管理
- 商品管理
- 订单管理
- 系统配置

**主要组件**:
- `AdminController`: 管理员请求处理
- `AdminService`: 管理员业务逻辑
- `StatisticsService`: 统计分析服务
- `ConfigService`: 配置管理服务

## 数据库设计

### 实体关系图
```
Users (用户表)
├── UserAddresses (用户地址表)
├── UserLoginLogs (登录日志表)
├── Orders (订单表)
├── ProductFavorites (商品收藏表)
├── ProductReviews (商品评价表)
└── ShoppingCarts (购物车表)

Products (商品表)
├── ProductImages (商品图片表)
├── ProductSpecifications (商品规格表)
├── ProductTagRelations (商品标签关联表)
├── ProductReviews (商品评价表)
├── ProductFavorites (商品收藏表)
├── OrderItems (订单商品表)
└── ShoppingCarts (购物车表)

Categories (商品分类表)
└── Products (商品表)

Orders (订单表)
├── OrderItems (订单商品表)
├── OrderAddresses (订单地址表)
├── Payments (支付记录表)
├── Refunds (退款记录表)
└── ShippingInfo (物流信息表)
```

### 核心实体模型

#### User 实体
```java
@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, length = 50)
    private String username;

    @Column(nullable = false, length = 100, unique = true)
    private String email;

    @Column(length = 20)
    private String phone;

    @Column(nullable = false)
    private String password;

    @Column(length = 500)
    private String avatar;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private UserRole role = UserRole.USER;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private UserStatus status = UserStatus.ACTIVE;

    @Column(name = "email_verified_at")
    private LocalDateTime emailVerifiedAt;

    @Column(name = "phone_verified_at")
    private LocalDateTime phoneVerifiedAt;

    @Column(name = "last_login_at")
    private LocalDateTime lastLoginAt;

    @Column(name = "last_login_ip", length = 45)
    private String lastLoginIp;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;
}
```

#### Product 实体
```java
@Entity
@Table(name = "products")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Product {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "category_id", nullable = false)
    private Long categoryId;

    @Column(nullable = false, length = 200)
    private String name;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal price;

    @Column(name = "original_price", precision = 10, scale = 2)
    private BigDecimal originalPrice;

    @Column(name = "cost_price", precision = 10, scale = 2)
    private BigDecimal costPrice;

    @Column(nullable = false)
    private Integer stock = 0;

    @Column(name = "min_stock", nullable = false)
    private Integer minStock = 0;

    @Column(nullable = false)
    private Integer sales = 0;

    @Column(nullable = false)
    private Integer views = 0;

    @Column(precision = 8, scale = 2)
    private BigDecimal weight;

    @Column(precision = 8, scale = 2)
    private BigDecimal volume;

    @Column(length = 100, unique = true)
    private String sku;

    @Column(length = 50)
    private String barcode;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ProductStatus status = ProductStatus.DRAFT;

    @Column(name = "is_featured", nullable = false)
    private Boolean isFeatured = false;

    @Column(name = "is_hot", nullable = false)
    private Boolean isHot = false;

    @Column(name = "is_new", nullable = false)
    private Boolean isNew = false;

    @Column(nullable = false)
    private Integer sort = 0;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;
}
```

#### Order 实体
```java
@Entity
@Table(name = "orders")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Order {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "order_no", nullable = false, length = 32, unique = true)
    private String orderNo;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OrderStatus status = OrderStatus.PENDING;

    @Enumerated(EnumType.STRING)
    @Column(name = "payment_status", nullable = false)
    private PaymentStatus paymentStatus = PaymentStatus.PENDING;

    @Enumerated(EnumType.STRING)
    @Column(name = "shipping_status", nullable = false)
    private ShippingStatus shippingStatus = ShippingStatus.PENDING;

    @Column(name = "total_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal totalAmount;

    @Column(name = "product_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal productAmount;

    @Column(name = "shipping_fee", nullable = false, precision = 10, scale = 2)
    private BigDecimal shippingFee = BigDecimal.ZERO;

    @Column(name = "discount_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal discountAmount = BigDecimal.ZERO;

    @Column(name = "coupon_amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal couponAmount = BigDecimal.ZERO;

    @Column(name = "payment_method", length = 20)
    private String paymentMethod;

    @Column(name = "payment_time")
    private LocalDateTime paymentTime;

    @Column(name = "shipped_time")
    private LocalDateTime shippedTime;

    @Column(name = "completed_time")
    private LocalDateTime completedTime;

    @Column(name = "cancelled_time")
    private LocalDateTime cancelledTime;

    @Column(name = "cancel_reason", length = 200)
    private String cancelReason;

    @Column(columnDefinition = "TEXT")
    private String note;

    @Column(name = "admin_note", columnDefinition = "TEXT")
    private String adminNote;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
}
```

## API 设计

### 统一响应格式
```java
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {
    private Integer code;
    private String message;
    private T data;

    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "success", data);
    }

    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(200, message, data);
    }

    public static <T> ApiResponse<T> error(Integer code, String message) {
        return new ApiResponse<>(code, message, null);
    }

    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(400, message, null);
    }
}
```

### 路由设计
```java
// 主要通过Controller注解和RequestMapping实现路由
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class BaseController {
    // 基础控制器，提供通用功能
}

// 认证相关路由
@RestController
@RequestMapping("/api/auth")
public class AuthController extends BaseController {
    @PostMapping("/login")
    @PostMapping("/register")
    @PostMapping("/logout")
    @PostMapping("/refresh")
}

// 用户相关路由
@RestController
@RequestMapping("/api/user")
@PreAuthorize("hasRole('USER')")
public class UserController extends BaseController {
    @GetMapping("/info")
    @PutMapping("/info")
    @PostMapping("/avatar")
    // ... 其他用户相关接口
}
```

### 认证控制器示例
```java
@RestController
@RequestMapping("/api/auth")
@Validated
public class AuthController {

    @Autowired
    private AuthService authService;

    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        LoginResponse response = authService.login(request);
        return ApiResponse.success("登录成功", response);
    }

    @PostMapping("/register")
    public ApiResponse<UserResponse> register(@Valid @RequestBody RegisterRequest request) {
        UserResponse response = authService.register(request);
        return ApiResponse.success("注册成功", response);
    }

    @PostMapping("/logout")
    @PreAuthorize("hasRole('USER')")
    public ApiResponse<Void> logout(HttpServletRequest request) {
        String token = extractToken(request);
        authService.logout(token);
        return ApiResponse.success("登出成功", null);
    }
}
```

## 过滤器和拦截器设计

### JWT 认证过滤器
```java
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private UserDetailsService userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                  HttpServletResponse response,
                                  FilterChain filterChain) throws ServletException, IOException {

        String token = extractTokenFromRequest(request);

        if (token != null && jwtTokenProvider.validateToken(token)) {
            String username = jwtTokenProvider.getUsernameFromToken(token);
            UserDetails userDetails = userDetailsService.loadUserByUsername(username);

            UsernamePasswordAuthenticationToken authentication =
                new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

            SecurityContextHolder.getContext().setAuthentication(authentication);
        }

        filterChain.doFilter(request, response);
    }

    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
```

### 权限验证配置
```java
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(csrf -> csrf.disable())
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/auth/**").permitAll()
                .requestMatchers("/api/products/**").permitAll()
                .requestMatchers("/api/categories/**").permitAll()
                .requestMatchers("/api/config/**").permitAll()
                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()
                .requestMatchers("/api/admin/**").hasRole("ADMIN")
                .anyRequest().authenticated()
            )
            .exceptionHandling(ex -> ex.authenticationEntryPoint(jwtAuthenticationEntryPoint))
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
```

## 错误处理

### 异常处理定义
```java
// 业务异常类
public class BusinessException extends RuntimeException {
    private final ErrorCode errorCode;

    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public BusinessException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public ErrorCode getErrorCode() {
        return errorCode;
    }
}

// 错误码枚举
public enum ErrorCode {
    SUCCESS(200, "操作成功"),
    PARAM_ERROR(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "权限不足"),
    NOT_FOUND(404, "资源不存在"),
    INTERNAL_ERROR(500, "系统内部错误"),

    // 业务错误码
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_ALREADY_EXISTS(1002, "用户已存在"),
    INVALID_PASSWORD(1003, "密码错误"),
    PRODUCT_NOT_FOUND(2001, "商品不存在"),
    INSUFFICIENT_STOCK(2002, "库存不足"),
    ORDER_NOT_FOUND(3001, "订单不存在"),
    ORDER_STATUS_ERROR(3002, "订单状态错误");

    private final int code;
    private final String message;

    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() { return code; }
    public String getMessage() { return message; }
}

// 全局异常处理器
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public ApiResponse<Void> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return ApiResponse.error(e.getErrorCode().getCode(), e.getMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse<Void> handleValidationException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.joining(", "));
        return ApiResponse.error(400, message);
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ApiResponse<Void> handleAccessDeniedException(AccessDeniedException e) {
        return ApiResponse.error(403, "权限不足");
    }

    @ExceptionHandler(Exception.class)
    public ApiResponse<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return ApiResponse.error(500, "系统内部错误");
    }
}
```

## 配置管理

### Maven 项目配置 (pom.xml)
```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.0</version>
        <relativePath/>
    </parent>

    <groupId>com.ecommerce</groupId>
    <artifactId>ecommerce-backend</artifactId>
    <version>1.0.0</version>
    <name>ecommerce-backend</name>
    <description>电商平台后端服务</description>

    <properties>
        <java.version>17</java.version>
        <mybatis-plus.version>3.5.4</mybatis-plus.version>
        <jjwt.version>0.11.5</jjwt.version>
        <thumbnailator.version>0.4.19</thumbnailator.version>
        <springdoc.version>2.2.0</springdoc.version>
    </properties>

    <dependencies>
        <!-- Spring Boot Starters -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Database -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>

        <!-- JWT -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>${jjwt.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>${jjwt.version}</version>
            <scope>runtime</scope>
        </dependency>

        <!-- Image Processing -->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>${thumbnailator.version}</version>
        </dependency>

        <!-- API Documentation -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>${springdoc.version}</version>
        </dependency>

        <!-- Utilities -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
```

### 应用配置文件
```yaml
# application.yml
server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: ecommerce-backend

  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 3000
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

# JWT配置
jwt:
  secret: ${JWT_SECRET:mySecretKey}
  access-token-expires-in: 86400  # 24小时
  refresh-token-expires-in: 604800  # 7天

# 文件上传配置
upload:
  path: ${UPLOAD_PATH:/uploads}
  domain: ${UPLOAD_DOMAIN:http://localhost:8080}
  allowed-types: jpg,jpeg,png,gif,webp

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deletedAt
      logic-delete-value: now()
      logic-not-delete-value: null

# 日志配置
logging:
  level:
    com.ecommerce: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

## 测试策略

### 单元测试
```java
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
@Transactional
@Rollback
class UserServiceTest {

    @Autowired
    private UserService userService;

    @Autowired
    private TestEntityManager entityManager;

    @Test
    @DisplayName("用户注册测试")
    void testUserRegistration() {
        // Given
        RegisterRequest request = new RegisterRequest();
        request.setUsername("testuser");
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        request.setPhone("***********");

        // When
        UserResponse response = userService.register(request);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getUsername()).isEqualTo("testuser");
        assertThat(response.getEmail()).isEqualTo("<EMAIL>");

        // 验证数据库中确实创建了用户
        User savedUser = entityManager.find(User.class, response.getId());
        assertThat(savedUser).isNotNull();
        assertThat(savedUser.getUsername()).isEqualTo("testuser");
    }

    @Test
    @DisplayName("重复邮箱注册应该抛出异常")
    void testDuplicateEmailRegistration() {
        // Given
        RegisterRequest request1 = new RegisterRequest();
        request1.setUsername("user1");
        request1.setEmail("<EMAIL>");
        request1.setPassword("password123");

        RegisterRequest request2 = new RegisterRequest();
        request2.setUsername("user2");
        request2.setEmail("<EMAIL>");
        request2.setPassword("password456");

        // When & Then
        userService.register(request1);
        assertThatThrownBy(() -> userService.register(request2))
            .isInstanceOf(BusinessException.class)
            .hasMessage("用户已存在");
    }
}
```

### 集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(locations = "classpath:application-test.yml")
@Transactional
class AuthControllerIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private UserService userService;

    @Test
    @DisplayName("完整的认证流程测试")
    void testAuthFlow() {
        // 1. 注册用户
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setUsername("testuser");
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setPassword("password123");
        registerRequest.setPhone("***********");

        ResponseEntity<ApiResponse> registerResponse = restTemplate.postForEntity(
            "/api/auth/register",
            registerRequest,
            ApiResponse.class
        );

        assertThat(registerResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(registerResponse.getBody().getCode()).isEqualTo(200);

        // 2. 登录用户
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setEmail("<EMAIL>");
        loginRequest.setPassword("password123");

        ResponseEntity<ApiResponse> loginResponse = restTemplate.postForEntity(
            "/api/auth/login",
            loginRequest,
            ApiResponse.class
        );

        assertThat(loginResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(loginResponse.getBody().getCode()).isEqualTo(200);

        // 3. 使用token访问受保护的接口
        Map<String, Object> loginData = (Map<String, Object>) loginResponse.getBody().getData();
        String token = (String) loginData.get("token");

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<ApiResponse> userInfoResponse = restTemplate.exchange(
            "/api/user/info",
            HttpMethod.GET,
            entity,
            ApiResponse.class
        );

        assertThat(userInfoResponse.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(userInfoResponse.getBody().getCode()).isEqualTo(200);
    }
}
```

## 部署方案

### Docker 配置
```dockerfile
# Dockerfile
FROM openjdk:17-jdk-slim as builder

WORKDIR /app
COPY pom.xml .
COPY src ./src

# 使用Maven构建应用
RUN apt-get update && apt-get install -y maven
RUN mvn clean package -DskipTests

FROM openjdk:17-jre-slim

WORKDIR /app

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制构建好的jar文件
COPY --from=builder /app/target/ecommerce-backend-*.jar app.jar

# 创建上传目录
RUN mkdir -p /uploads

EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

CMD ["java", "-jar", "app.jar"]
```

### Docker Compose 配置
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_USERNAME=ecommerce_user
      - DB_PASSWORD=ecommerce_password
      - JWT_SECRET=your-secret-key
      - REDIS_HOST=redis
      - UPLOAD_PATH=/uploads
    volumes:
      - ./uploads:/uploads
    depends_on:
      - db
      - redis

  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=ecommerce
      - MYSQL_USER=ecommerce_user
      - MYSQL_PASSWORD=ecommerce_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  mysql_data:
```

## 开发计划

### 第一阶段：基础设施 (1-2周)
1. 项目初始化和依赖配置
2. 数据库连接和实体模型定义
3. 基础中间件和错误处理
4. 配置管理和日志系统

### 第二阶段：核心功能 (2-3周)
1. 用户认证系统
2. 用户管理功能
3. 商品管理功能
4. 基础API接口

### 第三阶段：业务功能 (2-3周)
1. 订单管理系统
2. 支付集成
3. 文件上传功能
4. 商品搜索和筛选

### 第四阶段：高级功能 (1-2周)
1. 管理员功能
2. 数据统计和分析
3. 缓存优化
4. 性能优化

### 第五阶段：测试和部署 (1周)
1. 单元测试和集成测试
2. 性能测试
3. 部署配置
4. 文档完善

## 性能优化建议

### 数据库优化
- 合理使用索引
- 查询优化和分页
- 连接池配置
- 读写分离 (可选)

### 缓存策略
- Redis缓存热点数据
- 查询结果缓存
- 会话缓存
- 静态资源缓存

### 并发处理
- 异步处理
- 连接池管理
- 限流和熔断
- 负载均衡

## 安全考虑

### 认证安全
- JWT token安全
- 密码强度验证
- 登录失败限制
- 会话管理

### 数据安全
- SQL注入防护
- XSS防护
- CSRF防护
- 数据加密

### 接口安全
- 请求限流
- 参数验证
- 权限控制
- 审计日志

## 监控和日志

### 日志策略
- 结构化日志
- 日志级别管理
- 日志轮转
- 错误追踪

### 监控指标
- 请求响应时间
- 错误率统计
- 数据库性能
- 系统资源使用

---

**文档版本**: v1.0
**创建日期**: 2024-01-01
**维护者**: 开发团队

> 本文档基于前端接口文档和数据库设计文档制定，详细描述了使用Java Spring Boot构建电商平台后端的完整方案。包含技术选型、架构设计、模块划分、开发计划等各个方面的内容。
