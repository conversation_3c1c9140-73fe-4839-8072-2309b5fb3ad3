# 电商平台后端项目设计方案

## 项目概述

基于前端提供的接口文档和数据库设计，使用 Rust 语言构建高性能、类型安全的电商平台后端服务。

## 技术栈选择

### 核心框架
- **Web框架**: Axum 0.7+ - 高性能异步Web框架，基于tokio和hyper
- **数据库**: MySQL 8.0+ - 关系型数据库
- **ORM**: SeaORM 0.12+ - Rust原生异步ORM，支持代码生成和迁移
- **异步运行时**: Tokio 1.0+ - 异步运行时

### 认证与安全
- **JWT**: jsonwebtoken - JWT token生成和验证
- **密码加密**: bcrypt - 密码哈希
- **CORS**: tower-http - 跨域请求处理
- **限流**: tower - 请求限流中间件

### 序列化与配置
- **序列化**: serde + serde_json - JSON序列化/反序列化
- **配置管理**: config + dotenvy - 环境配置管理
- **验证**: validator - 数据验证

### 日志与监控
- **日志**: tracing + tracing-subscriber - 结构化日志
- **错误处理**: anyhow + thiserror - 错误处理
- **监控**: metrics - 性能指标收集

### 文件处理
- **文件上传**: multer - multipart/form-data处理
- **图片处理**: image - 图片压缩和处理
- **文件存储**: 本地存储 + 云存储支持


## 项目架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │────│   API Gateway   │────│   后端服务      │
│   (React/Vue)   │    │   (Nginx)       │    │   (Rust/Axum)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Redis缓存     │────│   MySQL数据库   │
                       │   (可选)        │    │                 │
                       └─────────────────┘    └─────────────────┘
```

### 分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  Handlers   │ │ Middleware  │ │   Routes    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                     Business Layer                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  Services   │ │    DTOs     │ │ Validators  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                   Data Access Layer                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Repositories│ │   Models    │ │ Migrations  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                  Infrastructure Layer                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  Database   │ │    Cache    │ │ File Storage│           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 项目目录结构

```
ecommerce-backend/
├── Cargo.toml                    # 项目依赖配置
├── .env.example                  # 环境变量示例
├── .gitignore                    # Git忽略文件
├── README.md                     # 项目说明
├── docker-compose.yml            # Docker编排文件
├── Dockerfile                    # Docker镜像构建
├── migration/                    # 数据库迁移文件
│   ├── src/
│   │   ├── lib.rs
│   │   └── m20240101_000001_create_users_table.rs
│   └── Cargo.toml
├── entity/                       # 数据库实体定义
│   ├── src/
│   │   ├── lib.rs
│   │   ├── users.rs
│   │   ├── products.rs
│   │   ├── orders.rs
│   │   └── ...
│   └── Cargo.toml
├── src/
│   ├── main.rs                   # 应用程序入口
│   ├── lib.rs                    # 库入口
│   ├── app.rs                    # 应用程序状态和配置
│   ├── config/                   # 配置管理
│   │   ├── mod.rs
│   │   ├── database.rs           # 数据库配置
│   │   ├── jwt.rs                # JWT配置
│   │   └── app_config.rs         # 应用配置
│   ├── handlers/                 # HTTP请求处理器
│   │   ├── mod.rs
│   │   ├── auth.rs               # 认证相关处理器
│   │   ├── users.rs              # 用户相关处理器
│   │   ├── products.rs           # 商品相关处理器
│   │   ├── orders.rs             # 订单相关处理器
│   │   ├── admin.rs              # 管理员相关处理器
│   │   └── uploads.rs            # 文件上传处理器
│   ├── services/                 # 业务逻辑服务
│   │   ├── mod.rs
│   │   ├── auth_service.rs       # 认证服务
│   │   ├── user_service.rs       # 用户服务
│   │   ├── product_service.rs    # 商品服务
│   │   ├── order_service.rs      # 订单服务
│   │   ├── payment_service.rs    # 支付服务
│   │   └── upload_service.rs     # 文件上传服务
│   ├── repositories/             # 数据访问层
│   │   ├── mod.rs
│   │   ├── user_repository.rs    # 用户数据访问
│   │   ├── product_repository.rs # 商品数据访问
│   │   └── order_repository.rs   # 订单数据访问
│   ├── dto/                      # 数据传输对象
│   │   ├── mod.rs
│   │   ├── auth_dto.rs           # 认证相关DTO
│   │   ├── user_dto.rs           # 用户相关DTO
│   │   ├── product_dto.rs        # 商品相关DTO
│   │   ├── order_dto.rs          # 订单相关DTO
│   │   └── common_dto.rs         # 通用DTO
│   ├── middleware/               # 中间件
│   │   ├── mod.rs
│   │   ├── auth.rs               # JWT认证中间件
│   │   ├── cors.rs               # CORS中间件
│   │   ├── logging.rs            # 日志中间件
│   │   └── rate_limit.rs         # 限流中间件
│   ├── utils/                    # 工具函数
│   │   ├── mod.rs
│   │   ├── jwt.rs                # JWT工具
│   │   ├── password.rs           # 密码处理
│   │   ├── validation.rs         # 数据验证
│   │   ├── pagination.rs         # 分页工具
│   │   └── file_utils.rs         # 文件处理工具
│   ├── errors/                   # 错误处理
│   │   ├── mod.rs
│   │   ├── app_error.rs          # 应用错误定义
│   │   └── error_handler.rs      # 错误处理器
│   ├── routes/                   # 路由定义
│   │   ├── mod.rs
│   │   ├── auth_routes.rs        # 认证路由
│   │   ├── user_routes.rs        # 用户路由
│   │   ├── product_routes.rs     # 商品路由
│   │   ├── order_routes.rs       # 订单路由
│   │   └── admin_routes.rs       # 管理员路由
│   └── tests/                    # 测试文件
│       ├── mod.rs
│       ├── integration/          # 集成测试
│       └── unit/                 # 单元测试
├── docs/                         # 文档
│   ├── api.md                    # API文档
│   ├── deployment.md             # 部署文档
│   └── development.md            # 开发文档
└── scripts/                      # 脚本文件
    ├── init_db.sh                # 数据库初始化脚本
    ├── migrate.sh                # 数据库迁移脚本
    └── deploy.sh                 # 部署脚本
```

## 核心模块设计

### 1. 认证授权模块 (Auth)
**功能**:
- 用户登录/注册
- JWT token生成和验证
- 刷新token机制
- 密码加密和验证

**主要组件**:
- `AuthHandler`: 处理认证相关HTTP请求
- `AuthService`: 认证业务逻辑
- `JwtMiddleware`: JWT验证中间件
- `AuthDto`: 认证相关数据传输对象

### 2. 用户管理模块 (Users)
**功能**:
- 用户信息CRUD
- 用户地址管理
- 用户头像上传
- 用户统计信息

**主要组件**:
- `UserHandler`: 用户相关请求处理
- `UserService`: 用户业务逻辑
- `UserRepository`: 用户数据访问
- `UserDto`: 用户相关DTO

### 3. 商品管理模块 (Products)
**功能**:
- 商品CRUD操作
- 商品分类管理
- 商品搜索和筛选
- 商品图片管理
- 商品评价系统
- 商品收藏功能

**主要组件**:
- `ProductHandler`: 商品请求处理
- `ProductService`: 商品业务逻辑
- `CategoryService`: 分类业务逻辑
- `ProductRepository`: 商品数据访问

### 4. 订单管理模块 (Orders)
**功能**:
- 订单创建和管理
- 订单状态流转
- 订单商品管理
- 订单地址管理
- 物流信息管理

**主要组件**:
- `OrderHandler`: 订单请求处理
- `OrderService`: 订单业务逻辑
- `ShippingService`: 物流业务逻辑
- `OrderRepository`: 订单数据访问

### 5. 支付管理模块 (Payments)
**功能**:
- 支付订单处理
- 支付状态管理
- 退款处理
- 支付方式配置

**主要组件**:
- `PaymentHandler`: 支付请求处理
- `PaymentService`: 支付业务逻辑
- `RefundService`: 退款业务逻辑
- `PaymentRepository`: 支付数据访问

### 6. 文件上传模块 (Uploads)
**功能**:
- 图片上传和处理
- 文件存储管理
- 图片压缩和格式转换
- 文件访问权限控制

**主要组件**:
- `UploadHandler`: 文件上传处理
- `UploadService`: 文件处理业务逻辑
- `FileStorage`: 文件存储抽象
- `ImageProcessor`: 图片处理工具

### 7. 管理员模块 (Admin)
**功能**:
- 数据统计和分析
- 用户管理
- 商品管理
- 订单管理
- 系统配置

**主要组件**:
- `AdminHandler`: 管理员请求处理
- `AdminService`: 管理员业务逻辑
- `StatisticsService`: 统计分析服务
- `ConfigService`: 配置管理服务

## 数据库设计

### 实体关系图
```
Users (用户表)
├── UserAddresses (用户地址表)
├── UserLoginLogs (登录日志表)
├── Orders (订单表)
├── ProductFavorites (商品收藏表)
├── ProductReviews (商品评价表)
└── ShoppingCarts (购物车表)

Products (商品表)
├── ProductImages (商品图片表)
├── ProductSpecifications (商品规格表)
├── ProductTagRelations (商品标签关联表)
├── ProductReviews (商品评价表)
├── ProductFavorites (商品收藏表)
├── OrderItems (订单商品表)
└── ShoppingCarts (购物车表)

Categories (商品分类表)
└── Products (商品表)

Orders (订单表)
├── OrderItems (订单商品表)
├── OrderAddresses (订单地址表)
├── Payments (支付记录表)
├── Refunds (退款记录表)
└── ShippingInfo (物流信息表)
```

### 核心实体模型

#### User 实体
```rust
#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "users")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub username: String,
    pub email: String,
    pub phone: Option<String>,
    pub password: String,
    pub avatar: Option<String>,
    pub role: UserRole,
    pub status: UserStatus,
    pub email_verified_at: Option<DateTime>,
    pub phone_verified_at: Option<DateTime>,
    pub last_login_at: Option<DateTime>,
    pub last_login_ip: Option<String>,
    pub created_at: DateTime,
    pub updated_at: DateTime,
    pub deleted_at: Option<DateTime>,
}
```

#### Product 实体
```rust
#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "products")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub category_id: i64,
    pub name: String,
    pub description: Option<String>,
    pub price: Decimal,
    pub original_price: Option<Decimal>,
    pub cost_price: Option<Decimal>,
    pub stock: u32,
    pub min_stock: u32,
    pub sales: u32,
    pub views: u32,
    pub weight: Option<Decimal>,
    pub volume: Option<Decimal>,
    pub sku: Option<String>,
    pub barcode: Option<String>,
    pub status: ProductStatus,
    pub is_featured: bool,
    pub is_hot: bool,
    pub is_new: bool,
    pub sort: u32,
    pub created_at: DateTime,
    pub updated_at: DateTime,
    pub deleted_at: Option<DateTime>,
}
```

#### Order 实体
```rust
#[derive(Clone, Debug, PartialEq, DeriveEntityModel)]
#[sea_orm(table_name = "orders")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i64,
    pub order_no: String,
    pub user_id: i64,
    pub status: OrderStatus,
    pub payment_status: PaymentStatus,
    pub shipping_status: ShippingStatus,
    pub total_amount: Decimal,
    pub product_amount: Decimal,
    pub shipping_fee: Decimal,
    pub discount_amount: Decimal,
    pub coupon_amount: Decimal,
    pub payment_method: Option<String>,
    pub payment_time: Option<DateTime>,
    pub shipped_time: Option<DateTime>,
    pub completed_time: Option<DateTime>,
    pub cancelled_time: Option<DateTime>,
    pub cancel_reason: Option<String>,
    pub note: Option<String>,
    pub admin_note: Option<String>,
    pub created_at: DateTime,
    pub updated_at: DateTime,
}
```

## API 设计

### 统一响应格式
```rust
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub code: u16,
    pub message: String,
    pub data: Option<T>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            code: 200,
            message: "success".to_string(),
            data: Some(data),
        }
    }

    pub fn error(code: u16, message: String) -> Self {
        Self {
            code,
            message,
            data: None,
        }
    }
}
```

### 路由设计
```rust
// 主路由配置
pub fn create_routes() -> Router<AppState> {
    Router::new()
        .nest("/api/auth", auth_routes())
        .nest("/api/user", user_routes())
        .nest("/api/products", product_routes())
        .nest("/api/categories", category_routes())
        .nest("/api/orders", order_routes())
        .nest("/api/admin", admin_routes())
        .nest("/api/upload", upload_routes())
        .nest("/api/config", config_routes())
        .layer(cors_layer())
        .layer(logging_layer())
        .layer(rate_limit_layer())
}
```

### 认证路由示例
```rust
pub fn auth_routes() -> Router<AppState> {
    Router::new()
        .route("/login", post(login_handler))
        .route("/register", post(register_handler))
        .route("/logout", post(logout_handler).layer(auth_middleware()))
        .route("/refresh", post(refresh_token_handler))
}
```

## 中间件设计

### JWT 认证中间件
```rust
pub async fn auth_middleware<B>(
    State(state): State<AppState>,
    mut req: Request<B>,
    next: Next<B>,
) -> Result<Response, AppError> {
    let auth_header = req
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok())
        .and_then(|header| header.strip_prefix("Bearer "));

    let token = auth_header.ok_or(AppError::Unauthorized)?;

    let claims = verify_jwt_token(token, &state.config.jwt_secret)?;

    req.extensions_mut().insert(claims);

    Ok(next.run(req).await)
}
```

### 权限验证中间件
```rust
pub fn require_role(required_role: UserRole) -> impl Fn(Request<Body>, Next<Body>) -> BoxFuture<'static, Result<Response, AppError>> {
    move |req: Request<Body>, next: Next<Body>| {
        let required_role = required_role.clone();
        Box::pin(async move {
            let claims = req.extensions().get::<JwtClaims>()
                .ok_or(AppError::Unauthorized)?;

            if claims.role != required_role && claims.role != UserRole::Admin {
                return Err(AppError::Forbidden);
            }

            Ok(next.run(req).await)
        })
    }
}
```

## 错误处理

### 错误类型定义
```rust
#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] sea_orm::DbErr),

    #[error("Validation error: {0}")]
    Validation(String),

    #[error("Authentication failed")]
    Unauthorized,

    #[error("Access forbidden")]
    Forbidden,

    #[error("Resource not found")]
    NotFound,

    #[error("Internal server error")]
    Internal(#[from] anyhow::Error),
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, message) = match self {
            AppError::Validation(msg) => (StatusCode::BAD_REQUEST, msg),
            AppError::Unauthorized => (StatusCode::UNAUTHORIZED, "Unauthorized".to_string()),
            AppError::Forbidden => (StatusCode::FORBIDDEN, "Forbidden".to_string()),
            AppError::NotFound => (StatusCode::NOT_FOUND, "Not Found".to_string()),
            _ => (StatusCode::INTERNAL_SERVER_ERROR, "Internal Server Error".to_string()),
        };

        let response = ApiResponse::<()>::error(status.as_u16(), message);
        (status, Json(response)).into_response()
    }
}
```

## 配置管理

### 应用配置结构
```rust
#[derive(Debug, Clone, Deserialize)]
pub struct AppConfig {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub jwt: JwtConfig,
    pub upload: UploadConfig,
    pub redis: Option<RedisConfig>,
}

#[derive(Debug, Clone, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
    pub cors_origins: Vec<String>,
}

#[derive(Debug, Clone, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: u32,
}

#[derive(Debug, Clone, Deserialize)]
pub struct JwtConfig {
    pub secret: String,
    pub access_token_expires_in: i64,
    pub refresh_token_expires_in: i64,
}
```

## 测试策略

### 单元测试
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_user_registration() {
        let config = test_config();
        let db = test_database().await;
        let service = UserService::new(db);

        let request = RegisterRequest {
            username: "testuser".to_string(),
            email: "<EMAIL>".to_string(),
            password: "password123".to_string(),
            phone: Some("13800138000".to_string()),
        };

        let result = service.register(request).await;
        assert!(result.is_ok());
    }
}
```

### 集成测试
```rust
#[tokio::test]
async fn test_auth_flow() {
    let app = create_test_app().await;

    // 注册用户
    let register_response = app
        .oneshot(
            Request::builder()
                .method("POST")
                .uri("/api/auth/register")
                .header("content-type", "application/json")
                .body(Body::from(r#"{"username":"test","email":"<EMAIL>","password":"password123"}"#))
                .unwrap(),
        )
        .await
        .unwrap();

    assert_eq!(register_response.status(), StatusCode::OK);

    // 登录用户
    let login_response = app
        .oneshot(
            Request::builder()
                .method("POST")
                .uri("/api/auth/login")
                .header("content-type", "application/json")
                .body(Body::from(r#"{"email":"<EMAIL>","password":"password123"}"#))
                .unwrap(),
        )
        .await
        .unwrap();

    assert_eq!(login_response.status(), StatusCode::OK);
}
```

## 部署方案

### Docker 配置
```dockerfile
# Dockerfile
FROM rust:1.75 as builder

WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bookworm-slim

RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

COPY --from=builder /app/target/release/ecommerce-backend /usr/local/bin/

EXPOSE 3000

CMD ["ecommerce-backend"]
```

### Docker Compose 配置
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=mysql://user:password@db:3306/ecommerce
      - JWT_SECRET=your-secret-key
      - RUST_LOG=info
    depends_on:
      - db
      - redis

  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=ecommerce
      - MYSQL_USER=user
      - MYSQL_PASSWORD=password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  mysql_data:
```

## 开发计划

### 第一阶段：基础设施 (1-2周)
1. 项目初始化和依赖配置
2. 数据库连接和实体模型定义
3. 基础中间件和错误处理
4. 配置管理和日志系统

### 第二阶段：核心功能 (2-3周)
1. 用户认证系统
2. 用户管理功能
3. 商品管理功能
4. 基础API接口

### 第三阶段：业务功能 (2-3周)
1. 订单管理系统
2. 支付集成
3. 文件上传功能
4. 商品搜索和筛选

### 第四阶段：高级功能 (1-2周)
1. 管理员功能
2. 数据统计和分析
3. 缓存优化
4. 性能优化

### 第五阶段：测试和部署 (1周)
1. 单元测试和集成测试
2. 性能测试
3. 部署配置
4. 文档完善

## 性能优化建议

### 数据库优化
- 合理使用索引
- 查询优化和分页
- 连接池配置
- 读写分离 (可选)

### 缓存策略
- Redis缓存热点数据
- 查询结果缓存
- 会话缓存
- 静态资源缓存

### 并发处理
- 异步处理
- 连接池管理
- 限流和熔断
- 负载均衡

## 安全考虑

### 认证安全
- JWT token安全
- 密码强度验证
- 登录失败限制
- 会话管理

### 数据安全
- SQL注入防护
- XSS防护
- CSRF防护
- 数据加密

### 接口安全
- 请求限流
- 参数验证
- 权限控制
- 审计日志

## 监控和日志

### 日志策略
- 结构化日志
- 日志级别管理
- 日志轮转
- 错误追踪

### 监控指标
- 请求响应时间
- 错误率统计
- 数据库性能
- 系统资源使用

---

**文档版本**: v1.0
**创建日期**: 2024-01-01
**维护者**: 开发团队

> 本文档基于前端接口文档和数据库设计文档制定，详细描述了使用Rust构建电商平台后端的完整方案。包含技术选型、架构设计、模块划分、开发计划等各个方面的内容。
