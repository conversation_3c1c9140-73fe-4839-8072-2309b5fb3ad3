# 电商平台数据库表设计

## 概述

本文档详细描述了电商平台的数据库表结构设计，包含用户管理、商品管理、订单管理、支付管理等核心业务模块。

## 数据库信息

- **数据库类型**: MySQL 8.0+
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB

## 表设计规范

### 命名规范
- 表名：小写字母，单词间用下划线分隔，如 `user_addresses`
- 字段名：小写字母，单词间用下划线分隔，如 `created_at`
- 索引名：`idx_表名_字段名` 或 `uk_表名_字段名`（唯一索引）

### 通用字段
- `id`: 主键，BIGINT UNSIGNED AUTO_INCREMENT
- `created_at`: 创建时间，TIMESTAMP DEFAULT CURRENT_TIMESTAMP
- `updated_at`: 更新时间，TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
- `deleted_at`: 软删除时间，TIMESTAMP NULL（需要软删除的表）

## 1. 用户相关表

### 1.1 用户表 (users)

```sql
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `password` varchar(255) NOT NULL COMMENT '密码哈希',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `role` enum('user','admin') NOT NULL DEFAULT 'user' COMMENT '用户角色',
  `status` enum('active','inactive','banned') NOT NULL DEFAULT 'active' COMMENT '用户状态',
  `email_verified_at` timestamp NULL DEFAULT NULL COMMENT '邮箱验证时间',
  `phone_verified_at` timestamp NULL DEFAULT NULL COMMENT '手机验证时间',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_users_email` (`email`),
  UNIQUE KEY `uk_users_phone` (`phone`),
  KEY `idx_users_username` (`username`),
  KEY `idx_users_status` (`status`),
  KEY `idx_users_role` (`role`),
  KEY `idx_users_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

### 1.2 用户地址表 (user_addresses)

```sql
CREATE TABLE `user_addresses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `name` varchar(50) NOT NULL COMMENT '收货人姓名',
  `phone` varchar(20) NOT NULL COMMENT '收货人电话',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) NOT NULL COMMENT '区县',
  `address` varchar(200) NOT NULL COMMENT '详细地址',
  `postal_code` varchar(10) DEFAULT NULL COMMENT '邮政编码',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认地址',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_addresses_user_id` (`user_id`),
  KEY `idx_user_addresses_is_default` (`is_default`),
  CONSTRAINT `fk_user_addresses_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户地址表';
```

### 1.3 用户登录日志表 (user_login_logs)

```sql
CREATE TABLE `user_login_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `login_ip` varchar(45) NOT NULL COMMENT '登录IP',
  `user_agent` text COMMENT '用户代理',
  `login_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  `status` enum('success','failed') NOT NULL COMMENT '登录状态',
  `fail_reason` varchar(100) DEFAULT NULL COMMENT '失败原因',
  PRIMARY KEY (`id`),
  KEY `idx_user_login_logs_user_id` (`user_id`),
  KEY `idx_user_login_logs_login_at` (`login_at`),
  KEY `idx_user_login_logs_status` (`status`),
  CONSTRAINT `fk_user_login_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录日志表';
```

## 2. 商品相关表

### 2.1 商品分类表 (categories)

```sql
CREATE TABLE `categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '父分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `image` varchar(500) DEFAULT NULL COMMENT '分类图片',
  `level` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '分类层级',
  `sort` int unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_categories_parent_id` (`parent_id`),
  KEY `idx_categories_level` (`level`),
  KEY `idx_categories_sort` (`sort`),
  KEY `idx_categories_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';
```

### 2.2 商品表 (products)

```sql
CREATE TABLE `products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `category_id` bigint unsigned NOT NULL COMMENT '分类ID',
  `name` varchar(200) NOT NULL COMMENT '商品名称',
  `description` text COMMENT '商品描述',
  `price` decimal(10,2) NOT NULL COMMENT '现价',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `cost_price` decimal(10,2) DEFAULT NULL COMMENT '成本价',
  `stock` int unsigned NOT NULL DEFAULT '0' COMMENT '库存数量',
  `min_stock` int unsigned NOT NULL DEFAULT '0' COMMENT '最低库存预警',
  `sales` int unsigned NOT NULL DEFAULT '0' COMMENT '销量',
  `views` int unsigned NOT NULL DEFAULT '0' COMMENT '浏览量',
  `weight` decimal(8,2) DEFAULT NULL COMMENT '重量(kg)',
  `volume` decimal(8,2) DEFAULT NULL COMMENT '体积(立方米)',
  `sku` varchar(100) DEFAULT NULL COMMENT 'SKU编码',
  `barcode` varchar(50) DEFAULT NULL COMMENT '条形码',
  `status` enum('active','inactive','draft') NOT NULL DEFAULT 'draft' COMMENT '商品状态',
  `is_featured` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否推荐',
  `is_hot` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否热门',
  `is_new` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否新品',
  `sort` int unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_products_sku` (`sku`),
  KEY `idx_products_category_id` (`category_id`),
  KEY `idx_products_price` (`price`),
  KEY `idx_products_status` (`status`),
  KEY `idx_products_sales` (`sales`),
  KEY `idx_products_is_featured` (`is_featured`),
  KEY `idx_products_is_hot` (`is_hot`),
  KEY `idx_products_is_new` (`is_new`),
  KEY `idx_products_created_at` (`created_at`),
  CONSTRAINT `fk_products_category_id` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';
```

### 2.3 商品图片表 (product_images)

```sql
CREATE TABLE `product_images` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '图片ID',
  `product_id` bigint unsigned NOT NULL COMMENT '商品ID',
  `url` varchar(500) NOT NULL COMMENT '图片URL',
  `alt` varchar(200) DEFAULT NULL COMMENT '图片描述',
  `sort` int unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `is_primary` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否主图',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_images_product_id` (`product_id`),
  KEY `idx_product_images_sort` (`sort`),
  KEY `idx_product_images_is_primary` (`is_primary`),
  CONSTRAINT `fk_product_images_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品图片表';
```

### 2.4 商品标签表 (product_tags)

```sql
CREATE TABLE `product_tags` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `color` varchar(7) DEFAULT '#1890ff' COMMENT '标签颜色',
  `sort` int unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_tags_name` (`name`),
  KEY `idx_product_tags_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品标签表';
```

### 2.5 商品标签关联表 (product_tag_relations)

```sql
CREATE TABLE `product_tag_relations` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `product_id` bigint unsigned NOT NULL COMMENT '商品ID',
  `tag_id` bigint unsigned NOT NULL COMMENT '标签ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_tag_relations` (`product_id`,`tag_id`),
  KEY `idx_product_tag_relations_product_id` (`product_id`),
  KEY `idx_product_tag_relations_tag_id` (`tag_id`),
  CONSTRAINT `fk_product_tag_relations_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_product_tag_relations_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `product_tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品标签关联表';
```

### 2.6 商品规格表 (product_specifications)

```sql
CREATE TABLE `product_specifications` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '规格ID',
  `product_id` bigint unsigned NOT NULL COMMENT '商品ID',
  `name` varchar(50) NOT NULL COMMENT '规格名称',
  `value` varchar(200) NOT NULL COMMENT '规格值',
  `sort` int unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_specifications_product_id` (`product_id`),
  KEY `idx_product_specifications_sort` (`sort`),
  CONSTRAINT `fk_product_specifications_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品规格表';
```

### 2.7 商品收藏表 (product_favorites)

```sql
CREATE TABLE `product_favorites` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `product_id` bigint unsigned NOT NULL COMMENT '商品ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_favorites` (`user_id`,`product_id`),
  KEY `idx_product_favorites_user_id` (`user_id`),
  KEY `idx_product_favorites_product_id` (`product_id`),
  KEY `idx_product_favorites_created_at` (`created_at`),
  CONSTRAINT `fk_product_favorites_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_product_favorites_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品收藏表';
```

### 2.8 商品评价表 (product_reviews)

```sql
CREATE TABLE `product_reviews` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `product_id` bigint unsigned NOT NULL COMMENT '商品ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_id` bigint unsigned DEFAULT NULL COMMENT '订单ID',
  `rating` tinyint unsigned NOT NULL COMMENT '评分(1-5)',
  `content` text COMMENT '评价内容',
  `images` json DEFAULT NULL COMMENT '评价图片',
  `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending' COMMENT '审核状态',
  `is_anonymous` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否匿名',
  `reply_content` text COMMENT '商家回复',
  `reply_at` timestamp NULL DEFAULT NULL COMMENT '回复时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_product_reviews_product_id` (`product_id`),
  KEY `idx_product_reviews_user_id` (`user_id`),
  KEY `idx_product_reviews_order_id` (`order_id`),
  KEY `idx_product_reviews_rating` (`rating`),
  KEY `idx_product_reviews_status` (`status`),
  KEY `idx_product_reviews_created_at` (`created_at`),
  CONSTRAINT `fk_product_reviews_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_product_reviews_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品评价表';
```

## 3. 购物车相关表

### 3.1 购物车表 (shopping_carts)

```sql
CREATE TABLE `shopping_carts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '购物车ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `product_id` bigint unsigned NOT NULL COMMENT '商品ID',
  `quantity` int unsigned NOT NULL DEFAULT '1' COMMENT '数量',
  `price` decimal(10,2) NOT NULL COMMENT '加入时价格',
  `selected` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否选中',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_shopping_carts` (`user_id`,`product_id`),
  KEY `idx_shopping_carts_user_id` (`user_id`),
  KEY `idx_shopping_carts_product_id` (`product_id`),
  KEY `idx_shopping_carts_selected` (`selected`),
  CONSTRAINT `fk_shopping_carts_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_shopping_carts_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车表';
```

## 4. 订单相关表

### 4.1 订单表 (orders)

```sql
CREATE TABLE `orders` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `status` enum('pending','paid','shipped','completed','cancelled','refunded') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `payment_status` enum('pending','paid','failed','refunded','partial_refunded') NOT NULL DEFAULT 'pending' COMMENT '支付状态',
  `shipping_status` enum('pending','processing','shipped','delivered','returned') NOT NULL DEFAULT 'pending' COMMENT '物流状态',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `product_amount` decimal(10,2) NOT NULL COMMENT '商品金额',
  `shipping_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '运费',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `coupon_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠券金额',
  `payment_method` varchar(20) DEFAULT NULL COMMENT '支付方式',
  `payment_time` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `shipped_time` timestamp NULL DEFAULT NULL COMMENT '发货时间',
  `completed_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `cancelled_time` timestamp NULL DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(200) DEFAULT NULL COMMENT '取消原因',
  `note` text COMMENT '订单备注',
  `admin_note` text COMMENT '管理员备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_orders_order_no` (`order_no`),
  KEY `idx_orders_user_id` (`user_id`),
  KEY `idx_orders_status` (`status`),
  KEY `idx_orders_payment_status` (`payment_status`),
  KEY `idx_orders_shipping_status` (`shipping_status`),
  KEY `idx_orders_created_at` (`created_at`),
  KEY `idx_orders_payment_time` (`payment_time`),
  CONSTRAINT `fk_orders_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';
```

### 4.2 订单商品表 (order_items)

```sql
CREATE TABLE `order_items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '订单商品ID',
  `order_id` bigint unsigned NOT NULL COMMENT '订单ID',
  `product_id` bigint unsigned NOT NULL COMMENT '商品ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_image` varchar(500) DEFAULT NULL COMMENT '商品图片',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `price` decimal(10,2) NOT NULL COMMENT '商品单价',
  `quantity` int unsigned NOT NULL COMMENT '购买数量',
  `total_amount` decimal(10,2) NOT NULL COMMENT '小计金额',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_items_order_id` (`order_id`),
  KEY `idx_order_items_product_id` (`product_id`),
  CONSTRAINT `fk_order_items_order_id` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_order_items_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单商品表';
```

### 4.3 订单地址表 (order_addresses)

```sql
CREATE TABLE `order_addresses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '订单地址ID',
  `order_id` bigint unsigned NOT NULL COMMENT '订单ID',
  `name` varchar(50) NOT NULL COMMENT '收货人姓名',
  `phone` varchar(20) NOT NULL COMMENT '收货人电话',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) NOT NULL COMMENT '区县',
  `address` varchar(200) NOT NULL COMMENT '详细地址',
  `postal_code` varchar(10) DEFAULT NULL COMMENT '邮政编码',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_addresses_order_id` (`order_id`),
  CONSTRAINT `fk_order_addresses_order_id` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单地址表';
```

## 5. 支付相关表

### 5.1 支付记录表 (payments)

```sql
CREATE TABLE `payments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '支付ID',
  `payment_no` varchar(32) NOT NULL COMMENT '支付单号',
  `order_id` bigint unsigned NOT NULL COMMENT '订单ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `method` varchar(20) NOT NULL COMMENT '支付方式',
  `status` enum('pending','success','failed','cancelled','refunded') NOT NULL DEFAULT 'pending' COMMENT '支付状态',
  `third_party_no` varchar(100) DEFAULT NULL COMMENT '第三方支付单号',
  `third_party_response` json DEFAULT NULL COMMENT '第三方响应数据',
  `paid_at` timestamp NULL DEFAULT NULL COMMENT '支付完成时间',
  `failed_reason` varchar(200) DEFAULT NULL COMMENT '失败原因',
  `refund_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '退款金额',
  `refunded_at` timestamp NULL DEFAULT NULL COMMENT '退款时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payments_payment_no` (`payment_no`),
  KEY `idx_payments_order_id` (`order_id`),
  KEY `idx_payments_user_id` (`user_id`),
  KEY `idx_payments_status` (`status`),
  KEY `idx_payments_method` (`method`),
  KEY `idx_payments_created_at` (`created_at`),
  CONSTRAINT `fk_payments_order_id` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `fk_payments_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付记录表';
```

### 5.2 退款记录表 (refunds)

```sql
CREATE TABLE `refunds` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '退款ID',
  `refund_no` varchar(32) NOT NULL COMMENT '退款单号',
  `order_id` bigint unsigned NOT NULL COMMENT '订单ID',
  `payment_id` bigint unsigned NOT NULL COMMENT '支付ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '退款金额',
  `reason` varchar(200) NOT NULL COMMENT '退款原因',
  `description` text COMMENT '退款说明',
  `images` json DEFAULT NULL COMMENT '退款凭证图片',
  `status` enum('pending','approved','rejected','processing','completed','failed') NOT NULL DEFAULT 'pending' COMMENT '退款状态',
  `admin_note` text COMMENT '管理员备注',
  `third_party_no` varchar(100) DEFAULT NULL COMMENT '第三方退款单号',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_refunds_refund_no` (`refund_no`),
  KEY `idx_refunds_order_id` (`order_id`),
  KEY `idx_refunds_payment_id` (`payment_id`),
  KEY `idx_refunds_user_id` (`user_id`),
  KEY `idx_refunds_status` (`status`),
  KEY `idx_refunds_created_at` (`created_at`),
  CONSTRAINT `fk_refunds_order_id` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`),
  CONSTRAINT `fk_refunds_payment_id` FOREIGN KEY (`payment_id`) REFERENCES `payments` (`id`),
  CONSTRAINT `fk_refunds_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退款记录表';
```

## 6. 物流相关表

### 6.1 物流信息表 (shipping_info)

```sql
CREATE TABLE `shipping_info` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '物流ID',
  `order_id` bigint unsigned NOT NULL COMMENT '订单ID',
  `tracking_no` varchar(50) DEFAULT NULL COMMENT '快递单号',
  `carrier` varchar(50) DEFAULT NULL COMMENT '快递公司',
  `carrier_code` varchar(20) DEFAULT NULL COMMENT '快递公司编码',
  `status` enum('pending','picked_up','in_transit','out_for_delivery','delivered','exception','returned') NOT NULL DEFAULT 'pending' COMMENT '物流状态',
  `estimated_delivery` timestamp NULL DEFAULT NULL COMMENT '预计送达时间',
  `actual_delivery` timestamp NULL DEFAULT NULL COMMENT '实际送达时间',
  `shipping_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '运费',
  `weight` decimal(8,2) DEFAULT NULL COMMENT '包裹重量',
  `note` text COMMENT '物流备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_shipping_info_order_id` (`order_id`),
  KEY `idx_shipping_info_tracking_no` (`tracking_no`),
  KEY `idx_shipping_info_carrier_code` (`carrier_code`),
  KEY `idx_shipping_info_status` (`status`),
  CONSTRAINT `fk_shipping_info_order_id` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流信息表';
```

### 6.2 物流轨迹表 (shipping_tracks)

```sql
CREATE TABLE `shipping_tracks` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '轨迹ID',
  `shipping_id` bigint unsigned NOT NULL COMMENT '物流ID',
  `status` varchar(50) NOT NULL COMMENT '状态',
  `description` varchar(200) NOT NULL COMMENT '状态描述',
  `location` varchar(100) DEFAULT NULL COMMENT '所在地点',
  `occurred_at` timestamp NOT NULL COMMENT '发生时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_shipping_tracks_shipping_id` (`shipping_id`),
  KEY `idx_shipping_tracks_occurred_at` (`occurred_at`),
  CONSTRAINT `fk_shipping_tracks_shipping_id` FOREIGN KEY (`shipping_id`) REFERENCES `shipping_info` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物流轨迹表';
```

## 7. 优惠券相关表

### 7.1 优惠券表 (coupons)

```sql
CREATE TABLE `coupons` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '优惠券ID',
  `name` varchar(100) NOT NULL COMMENT '优惠券名称',
  `code` varchar(50) DEFAULT NULL COMMENT '优惠券代码',
  `type` enum('fixed','percent') NOT NULL COMMENT '优惠类型',
  `value` decimal(10,2) NOT NULL COMMENT '优惠值',
  `min_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最低使用金额',
  `max_discount` decimal(10,2) DEFAULT NULL COMMENT '最大优惠金额',
  `total_quantity` int unsigned NOT NULL COMMENT '发放总数',
  `used_quantity` int unsigned NOT NULL DEFAULT '0' COMMENT '已使用数量',
  `per_user_limit` int unsigned NOT NULL DEFAULT '1' COMMENT '每用户限用次数',
  `status` enum('active','inactive','expired') NOT NULL DEFAULT 'active' COMMENT '状态',
  `start_time` timestamp NOT NULL COMMENT '开始时间',
  `end_time` timestamp NOT NULL COMMENT '结束时间',
  `description` text COMMENT '使用说明',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_coupons_code` (`code`),
  KEY `idx_coupons_status` (`status`),
  KEY `idx_coupons_start_time` (`start_time`),
  KEY `idx_coupons_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠券表';
```

### 7.2 用户优惠券表 (user_coupons)

```sql
CREATE TABLE `user_coupons` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户优惠券ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `coupon_id` bigint unsigned NOT NULL COMMENT '优惠券ID',
  `order_id` bigint unsigned DEFAULT NULL COMMENT '使用的订单ID',
  `status` enum('unused','used','expired') NOT NULL DEFAULT 'unused' COMMENT '使用状态',
  `received_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
  `used_at` timestamp NULL DEFAULT NULL COMMENT '使用时间',
  `expired_at` timestamp NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_coupons_user_id` (`user_id`),
  KEY `idx_user_coupons_coupon_id` (`coupon_id`),
  KEY `idx_user_coupons_order_id` (`order_id`),
  KEY `idx_user_coupons_status` (`status`),
  KEY `idx_user_coupons_expired_at` (`expired_at`),
  CONSTRAINT `fk_user_coupons_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_coupons_coupon_id` FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_coupons_order_id` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户优惠券表';
```

## 8. 系统配置表

### 8.1 系统配置表 (system_configs)

```sql
CREATE TABLE `system_configs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `key` varchar(100) NOT NULL COMMENT '配置键',
  `value` text COMMENT '配置值',
  `type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string' COMMENT '值类型',
  `group` varchar(50) NOT NULL DEFAULT 'general' COMMENT '配置分组',
  `name` varchar(100) NOT NULL COMMENT '配置名称',
  `description` text COMMENT '配置描述',
  `is_public` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否公开',
  `sort` int unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_system_configs_key` (`key`),
  KEY `idx_system_configs_group` (`group`),
  KEY `idx_system_configs_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';
```

### 8.2 地区表 (regions)

```sql
CREATE TABLE `regions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '地区ID',
  `parent_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT '父级ID',
  `name` varchar(50) NOT NULL COMMENT '地区名称',
  `code` varchar(20) DEFAULT NULL COMMENT '地区编码',
  `level` tinyint unsigned NOT NULL COMMENT '层级',
  `sort` int unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_regions_parent_id` (`parent_id`),
  KEY `idx_regions_code` (`code`),
  KEY `idx_regions_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地区表';
```

## 9. 文件管理表

### 9.1 文件表 (files)

```sql
CREATE TABLE `files` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `filename` varchar(255) NOT NULL COMMENT '存储文件名',
  `path` varchar(500) NOT NULL COMMENT '文件路径',
  `url` varchar(500) NOT NULL COMMENT '访问URL',
  `mime_type` varchar(100) NOT NULL COMMENT 'MIME类型',
  `size` bigint unsigned NOT NULL COMMENT '文件大小',
  `hash` varchar(64) DEFAULT NULL COMMENT '文件哈希',
  `user_id` bigint unsigned DEFAULT NULL COMMENT '上传用户ID',
  `usage_type` varchar(50) DEFAULT NULL COMMENT '使用类型',
  `usage_id` bigint unsigned DEFAULT NULL COMMENT '关联ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_files_user_id` (`user_id`),
  KEY `idx_files_hash` (`hash`),
  KEY `idx_files_usage` (`usage_type`,`usage_id`),
  KEY `idx_files_created_at` (`created_at`),
  CONSTRAINT `fk_files_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件表';
```

## 10. 统计分析表

### 10.1 用户统计表 (user_statistics)

```sql
CREATE TABLE `user_statistics` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `total_orders` int unsigned NOT NULL DEFAULT '0' COMMENT '总订单数',
  `total_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '总消费金额',
  `total_products` int unsigned NOT NULL DEFAULT '0' COMMENT '购买商品种类数',
  `avg_order_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '平均订单金额',
  `last_order_at` timestamp NULL DEFAULT NULL COMMENT '最后下单时间',
  `favorite_category_id` bigint unsigned DEFAULT NULL COMMENT '最喜欢的分类ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_statistics_user_id` (`user_id`),
  KEY `idx_user_statistics_total_amount` (`total_amount`),
  KEY `idx_user_statistics_total_orders` (`total_orders`),
  CONSTRAINT `fk_user_statistics_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户统计表';
```

### 10.2 商品统计表 (product_statistics)

```sql
CREATE TABLE `product_statistics` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `product_id` bigint unsigned NOT NULL COMMENT '商品ID',
  `total_sales` int unsigned NOT NULL DEFAULT '0' COMMENT '总销量',
  `total_revenue` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '总销售额',
  `total_views` int unsigned NOT NULL DEFAULT '0' COMMENT '总浏览量',
  `total_favorites` int unsigned NOT NULL DEFAULT '0' COMMENT '总收藏数',
  `total_reviews` int unsigned NOT NULL DEFAULT '0' COMMENT '总评价数',
  `avg_rating` decimal(3,2) NOT NULL DEFAULT '0.00' COMMENT '平均评分',
  `conversion_rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '转化率',
  `last_sold_at` timestamp NULL DEFAULT NULL COMMENT '最后销售时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_product_statistics_product_id` (`product_id`),
  KEY `idx_product_statistics_total_sales` (`total_sales`),
  KEY `idx_product_statistics_total_revenue` (`total_revenue`),
  KEY `idx_product_statistics_avg_rating` (`avg_rating`),
  CONSTRAINT `fk_product_statistics_product_id` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品统计表';
```

### 10.3 日销售统计表 (daily_sales_statistics)

```sql
CREATE TABLE `daily_sales_statistics` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `date` date NOT NULL COMMENT '日期',
  `total_orders` int unsigned NOT NULL DEFAULT '0' COMMENT '订单总数',
  `total_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '销售总额',
  `total_users` int unsigned NOT NULL DEFAULT '0' COMMENT '下单用户数',
  `new_users` int unsigned NOT NULL DEFAULT '0' COMMENT '新用户数',
  `total_products_sold` int unsigned NOT NULL DEFAULT '0' COMMENT '商品销售总数',
  `avg_order_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '平均订单金额',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_daily_sales_statistics_date` (`date`),
  KEY `idx_daily_sales_statistics_total_amount` (`total_amount`),
  KEY `idx_daily_sales_statistics_total_orders` (`total_orders`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='日销售统计表';
```

## 11. 索引优化建议

### 11.1 复合索引建议

```sql
-- 订单查询优化
ALTER TABLE `orders` ADD INDEX `idx_orders_user_status_created` (`user_id`, `status`, `created_at`);

-- 商品搜索优化
ALTER TABLE `products` ADD INDEX `idx_products_category_status_price` (`category_id`, `status`, `price`);
ALTER TABLE `products` ADD INDEX `idx_products_status_featured_sales` (`status`, `is_featured`, `sales`);

-- 评价查询优化
ALTER TABLE `product_reviews` ADD INDEX `idx_product_reviews_product_status_created` (`product_id`, `status`, `created_at`);

-- 支付记录查询优化
ALTER TABLE `payments` ADD INDEX `idx_payments_user_status_created` (`user_id`, `status`, `created_at`);

-- 购物车查询优化
ALTER TABLE `shopping_carts` ADD INDEX `idx_shopping_carts_user_selected_updated` (`user_id`, `selected`, `updated_at`);
```

### 11.2 全文索引建议

```sql
-- 商品搜索全文索引
ALTER TABLE `products` ADD FULLTEXT INDEX `ft_products_name_description` (`name`, `description`);

-- 用户搜索全文索引
ALTER TABLE `users` ADD FULLTEXT INDEX `ft_users_username_email` (`username`, `email`);
```

## 12. 初始化数据

### 12.1 系统配置初始化

```sql
INSERT INTO `system_configs` (`key`, `value`, `type`, `group`, `name`, `description`, `is_public`) VALUES
('site_name', '电商平台', 'string', 'general', '网站名称', '网站的名称', 1),
('site_description', '专业的电商购物平台', 'string', 'general', '网站描述', '网站的描述信息', 1),
('free_shipping_amount', '99.00', 'number', 'shipping', '免运费金额', '满多少金额免运费', 1),
('default_shipping_fee', '8.00', 'number', 'shipping', '默认运费', '默认运费金额', 1),
('order_auto_cancel_minutes', '30', 'number', 'order', '订单自动取消时间', '未支付订单自动取消时间(分钟)', 0),
('order_auto_complete_days', '7', 'number', 'order', '订单自动完成时间', '发货后自动完成订单时间(天)', 0),
('product_review_auto_approve', 'false', 'boolean', 'review', '评价自动审核', '商品评价是否自动审核通过', 0);
```

### 12.2 默认分类初始化

```sql
INSERT INTO `categories` (`id`, `parent_id`, `name`, `description`, `level`, `sort`, `status`) VALUES
(1, 0, '生鲜食品', '新鲜健康的生鲜食品', 1, 1, 'active'),
(2, 1, '水果', '新鲜水果', 2, 1, 'active'),
(3, 1, '蔬菜', '新鲜蔬菜', 2, 2, 'active'),
(4, 1, '肉类', '新鲜肉类', 2, 3, 'active'),
(5, 0, '日用百货', '日常生活用品', 1, 2, 'active'),
(6, 5, '清洁用品', '家庭清洁用品', 2, 1, 'active'),
(7, 5, '个人护理', '个人护理用品', 2, 2, 'active');
```

### 12.3 默认管理员用户

```sql
INSERT INTO `users` (`username`, `email`, `password`, `role`, `status`, `email_verified_at`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 'active', NOW());
```

## 13. 数据库维护建议

### 13.1 定期清理建议

```sql
-- 清理过期的用户登录日志 (保留3个月)
DELETE FROM `user_login_logs` WHERE `login_at` < DATE_SUB(NOW(), INTERVAL 3 MONTH);

-- 清理过期的购物车数据 (保留30天)
DELETE FROM `shopping_carts` WHERE `updated_at` < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 清理过期的文件记录 (软删除商品的图片)
DELETE f FROM `files` f
LEFT JOIN `products` p ON f.usage_type = 'product' AND f.usage_id = p.id
WHERE p.deleted_at IS NOT NULL AND p.deleted_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 13.2 性能监控建议

```sql
-- 监控慢查询
-- 在 my.cnf 中设置:
-- slow_query_log = 1
-- slow_query_log_file = /var/log/mysql/slow.log
-- long_query_time = 2

-- 监控表大小
SELECT
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables
WHERE table_schema = 'ecommerce_db'
ORDER BY (data_length + index_length) DESC;
```

---

**文档版本**: v1.0
**数据库版本**: MySQL 8.0+
**最后更新**: 2024-01-01
**维护者**: 开发团队

> 注意：
> 1. 所有表都使用 InnoDB 存储引擎，支持事务和外键约束
> 2. 字符集使用 utf8mb4，支持完整的 Unicode 字符
> 3. 时间字段统一使用 timestamp 类型，便于时区处理
> 4. 金额字段使用 decimal 类型，避免浮点数精度问题
> 5. 建议定期备份数据库，并监控表大小和查询性能
