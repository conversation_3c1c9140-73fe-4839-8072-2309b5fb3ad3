# 电商平台后端接口文档

## 概述

本文档详细描述了电商平台前端项目所需的后端API接口。该平台包含用户端和管理员端，涵盖完整的电商业务流程。

## 基础信息

- **Base URL**: `/api`
- **认证方式**: JWT Bearer <PERSON>
- **请求格式**: JSON
- **响应格式**: JSON

## 统一响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "error message",
  "data": null
}
```

### 状态码说明
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权/登录过期
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 1. 认证授权模块

### 1.1 用户登录
- **接口**: `POST /auth/login`
- **描述**: 用户登录获取访问令牌
- **权限**: 公开

**请求参数**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_string",
    "user": {
      "id": 1,
      "username": "张三",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "avatar": "https://example.com/avatar.jpg",
      "role": "user"
    }
  }
}
```

### 1.2 用户注册
- **接口**: `POST /auth/register`
- **描述**: 用户注册新账号
- **权限**: 公开

**请求参数**:
```json
{
  "username": "张三",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "13800138000"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "user": {
      "id": 1,
      "username": "张三",
      "email": "<EMAIL>",
      "phone": "13800138000"
    }
  }
}
```

### 1.3 用户登出
- **接口**: `POST /auth/logout`
- **描述**: 用户登出，使token失效
- **权限**: 需要登录

**请求头**:
```
Authorization: Bearer {token}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

### 1.4 刷新Token
- **接口**: `POST /auth/refresh`
- **描述**: 使用refresh token获取新的access token
- **权限**: 公开

**请求参数**:
```json
{
  "refreshToken": "refresh_token_string"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "token": "new_access_token",
    "refreshToken": "new_refresh_token"
  }
}
```

## 2. 用户管理模块

### 2.1 获取用户信息
- **接口**: `GET /user/info`
- **描述**: 获取当前登录用户的详细信息
- **权限**: 需要登录

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "username": "张三",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "avatar": "https://example.com/avatar.jpg",
    "role": "user",
    "createTime": "2024-01-01T00:00:00Z",
    "updateTime": "2024-01-01T00:00:00Z"
  }
}
```

### 2.2 更新用户信息
- **接口**: `PUT /user/info`
- **描述**: 更新当前用户的基本信息
- **权限**: 需要登录

**请求参数**:
```json
{
  "username": "李四",
  "email": "<EMAIL>",
  "phone": "13900139000"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "user": {
      "id": 1,
      "username": "李四",
      "email": "<EMAIL>",
      "phone": "13900139000"
    }
  }
}
```

### 2.3 修改密码
- **接口**: `PUT /user/password`
- **描述**: 修改用户密码
- **权限**: 需要登录

**请求参数**:
```json
{
  "oldPassword": "old_password",
  "newPassword": "new_password"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": null
}
```

### 2.4 上传头像
- **接口**: `POST /user/avatar`
- **描述**: 上传用户头像
- **权限**: 需要登录
- **请求格式**: `multipart/form-data`

**请求参数**:
```
avatar: File (图片文件)
```

**响应数据**:
```json
{
  "code": 200,
  "message": "头像上传成功",
  "data": {
    "avatarUrl": "https://example.com/avatars/user_1_avatar.jpg"
  }
}
```

## 3. 用户地址管理

### 3.1 获取地址列表
- **接口**: `GET /user/addresses`
- **描述**: 获取当前用户的收货地址列表
- **权限**: 需要登录

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "张三",
      "phone": "13800138000",
      "province": "北京市",
      "city": "北京市",
      "district": "朝阳区",
      "address": "某某街道123号",
      "isDefault": true,
      "createTime": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 3.2 添加地址
- **接口**: `POST /user/addresses`
- **描述**: 添加新的收货地址
- **权限**: 需要登录

**请求参数**:
```json
{
  "name": "李四",
  "phone": "13900139000",
  "province": "上海市",
  "city": "上海市",
  "district": "浦东新区",
  "address": "某某路456号",
  "isDefault": false
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "地址添加成功",
  "data": {
    "address": {
      "id": 2,
      "name": "李四",
      "phone": "13900139000",
      "province": "上海市",
      "city": "上海市",
      "district": "浦东新区",
      "address": "某某路456号",
      "isDefault": false
    }
  }
}
```

### 3.3 更新地址
- **接口**: `PUT /user/addresses/{id}`
- **描述**: 更新指定的收货地址
- **权限**: 需要登录

**请求参数**:
```json
{
  "name": "王五",
  "phone": "13700137000",
  "province": "广东省",
  "city": "深圳市",
  "district": "南山区",
  "address": "某某大道789号",
  "isDefault": true
}
```

### 3.4 删除地址
- **接口**: `DELETE /user/addresses/{id}`
- **描述**: 删除指定的收货地址
- **权限**: 需要登录

### 3.5 设置默认地址
- **接口**: `PUT /user/addresses/{id}/default`
- **描述**: 将指定地址设为默认地址
- **权限**: 需要登录

## 4. 商品管理模块

### 4.1 获取商品列表
- **接口**: `GET /products`
- **描述**: 获取商品列表，支持分页和筛选
- **权限**: 公开

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 20)
- `categoryId`: 分类ID
- `minPrice`: 最低价格
- `maxPrice`: 最高价格
- `sortBy`: 排序字段 (price, sales, createTime)
- `sortOrder`: 排序方向 (asc, desc)

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "有机苹果",
        "description": "新鲜有机苹果，口感清脆甘甜",
        "price": 29.90,
        "originalPrice": 39.90,
        "image": "https://example.com/products/apple.jpg",
        "images": ["https://example.com/products/apple1.jpg"],
        "stock": 100,
        "categoryId": 1,
        "categoryName": "水果",
        "tags": ["有机", "新鲜"],
        "sales": 1250,
        "rating": 4.8,
        "reviewCount": 156
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20
  }
}
```

### 4.2 获取商品详情
- **接口**: `GET /products/{id}`
- **描述**: 获取指定商品的详细信息
- **权限**: 公开

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "有机苹果",
    "description": "新鲜有机苹果，口感清脆甘甜，富含维生素C和膳食纤维...",
    "price": 29.90,
    "originalPrice": 39.90,
    "images": [
      "https://example.com/products/apple1.jpg",
      "https://example.com/products/apple2.jpg"
    ],
    "stock": 100,
    "categoryId": 1,
    "categoryName": "水果",
    "tags": ["有机", "新鲜", "维生素C"],
    "sales": 1250,
    "rating": 4.8,
    "reviewCount": 156,
    "specifications": {
      "产地": "山东烟台",
      "重量": "500g/袋",
      "保质期": "7天"
    },
    "createTime": "2024-01-01T00:00:00Z"
  }
}
```

### 4.3 搜索商品
- **接口**: `GET /products/search`
- **描述**: 根据关键词搜索商品
- **权限**: 公开

**查询参数**:
- `keyword`: 搜索关键词 (必填)
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 20)
- `categoryId`: 分类ID
- `minPrice`: 最低价格
- `maxPrice`: 最高价格
- `sortBy`: 排序字段

**响应数据**: 同商品列表格式

### 4.4 获取热门商品
- **接口**: `GET /products/hot`
- **描述**: 获取热门商品列表
- **权限**: 公开

**查询参数**:
- `limit`: 返回数量 (默认: 10)

### 4.5 获取推荐商品
- **接口**: `GET /products/recommend`
- **描述**: 获取推荐商品列表
- **权限**: 公开

**查询参数**:
- `limit`: 返回数量 (默认: 10)
- `userId`: 用户ID (用于个性化推荐)

### 4.6 获取新品
- **接口**: `GET /products/new`
- **描述**: 获取最新上架商品
- **权限**: 公开

**查询参数**:
- `limit`: 返回数量 (默认: 10)

## 5. 商品分类模块

### 5.1 获取分类列表
- **接口**: `GET /categories`
- **描述**: 获取商品分类树形结构
- **权限**: 公开

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "生鲜食品",
      "parentId": 0,
      "level": 1,
      "sort": 1,
      "children": [
        {
          "id": 2,
          "name": "水果",
          "parentId": 1,
          "level": 2,
          "sort": 1,
          "children": []
        },
        {
          "id": 3,
          "name": "蔬菜",
          "parentId": 1,
          "level": 2,
          "sort": 2,
          "children": []
        }
      ]
    }
  ]
}
```

### 5.2 获取分类详情
- **接口**: `GET /categories/{id}`
- **描述**: 获取指定分类的详细信息
- **权限**: 公开

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "生鲜食品",
    "description": "新鲜健康的生鲜食品",
    "parentId": 0,
    "level": 1,
    "sort": 1,
    "image": "https://example.com/categories/fresh.jpg",
    "children": [
      {
        "id": 2,
        "name": "水果",
        "parentId": 1
      }
    ]
  }
}
```

## 6. 商品评价模块

### 6.1 获取商品评价
- **接口**: `GET /products/{id}/reviews`
- **描述**: 获取指定商品的评价列表
- **权限**: 公开

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 10)
- `rating`: 评分筛选 (1-5)

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "id": 1,
        "userId": 1,
        "username": "张三",
        "avatar": "https://example.com/avatars/user1.jpg",
        "rating": 5,
        "content": "苹果很新鲜，口感很好，包装也很仔细",
        "images": ["https://example.com/reviews/review1.jpg"],
        "createTime": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 156,
    "page": 1,
    "pageSize": 10,
    "avgRating": 4.8,
    "ratingStats": {
      "5": 120,
      "4": 25,
      "3": 8,
      "2": 2,
      "1": 1
    }
  }
}
```

### 6.2 添加商品评价
- **接口**: `POST /products/{id}/reviews`
- **描述**: 为商品添加评价
- **权限**: 需要登录

**请求参数**:
```json
{
  "rating": 5,
  "content": "商品质量很好，值得推荐",
  "images": ["https://example.com/reviews/my_review.jpg"]
}
```

## 7. 商品收藏模块

### 7.1 收藏商品
- **接口**: `POST /products/{id}/favorite`
- **描述**: 收藏指定商品
- **权限**: 需要登录

### 7.2 取消收藏
- **接口**: `DELETE /products/{id}/favorite`
- **描述**: 取消收藏指定商品
- **权限**: 需要登录

### 7.3 获取收藏列表
- **接口**: `GET /user/favorites`
- **描述**: 获取用户收藏的商品列表
- **权限**: 需要登录

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 20)

**响应数据**: 同商品列表格式

## 8. 订单管理模块

### 8.1 创建订单
- **接口**: `POST /orders`
- **描述**: 创建新订单
- **权限**: 需要登录

**请求参数**:
```json
{
  "items": [
    {
      "productId": 1,
      "quantity": 2,
      "price": 29.90
    }
  ],
  "addressId": 1,
  "paymentMethod": "alipay",
  "note": "请尽快发货"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "orderId": 1001,
    "orderNo": "ORD202401010001",
    "totalAmount": 59.80,
    "paymentUrl": "https://payment.example.com/pay/1001"
  }
}
```

### 8.2 获取订单列表
- **接口**: `GET /orders`
- **描述**: 获取当前用户的订单列表
- **权限**: 需要登录

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 10)
- `status`: 订单状态 (pending, paid, shipped, completed, cancelled)
- `startDate`: 开始日期
- `endDate`: 结束日期

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "id": 1001,
        "orderNo": "ORD202401010001",
        "status": "paid",
        "statusText": "已付款",
        "totalAmount": 59.80,
        "itemCount": 2,
        "items": [
          {
            "productId": 1,
            "productName": "有机苹果",
            "productImage": "https://example.com/products/apple.jpg",
            "price": 29.90,
            "quantity": 2
          }
        ],
        "createTime": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 25,
    "page": 1,
    "pageSize": 10
  }
}
```

### 8.3 获取订单详情
- **接口**: `GET /orders/{id}`
- **描述**: 获取指定订单的详细信息
- **权限**: 需要登录

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1001,
    "orderNo": "ORD202401010001",
    "status": "paid",
    "statusText": "已付款",
    "totalAmount": 59.80,
    "items": [
      {
        "productId": 1,
        "productName": "有机苹果",
        "productImage": "https://example.com/products/apple.jpg",
        "price": 29.90,
        "quantity": 2,
        "subtotal": 59.80
      }
    ],
    "address": {
      "name": "张三",
      "phone": "13800138000",
      "address": "北京市朝阳区某某街道123号"
    },
    "payment": {
      "method": "alipay",
      "methodText": "支付宝",
      "status": "paid",
      "paidTime": "2024-01-01T10:00:00Z"
    },
    "shipping": {
      "fee": 0,
      "trackingNo": "SF1234567890",
      "carrier": "顺丰速运",
      "status": "pending"
    },
    "timeline": [
      {
        "status": "created",
        "description": "订单已创建",
        "time": "2024-01-01T09:00:00Z"
      },
      {
        "status": "paid",
        "description": "订单已付款",
        "time": "2024-01-01T10:00:00Z"
      }
    ],
    "createTime": "2024-01-01T09:00:00Z",
    "updateTime": "2024-01-01T10:00:00Z"
  }
}
```

### 8.4 取消订单
- **接口**: `PUT /orders/{id}/cancel`
- **描述**: 取消指定订单
- **权限**: 需要登录

**请求参数**:
```json
{
  "reason": "不想要了"
}
```

### 8.5 确认收货
- **接口**: `PUT /orders/{id}/confirm`
- **描述**: 确认收货完成订单
- **权限**: 需要登录

### 8.6 申请退款
- **接口**: `POST /orders/{id}/refund`
- **描述**: 申请订单退款
- **权限**: 需要登录

**请求参数**:
```json
{
  "reason": "商品有质量问题",
  "amount": 59.80,
  "description": "收到的苹果有腐烂现象",
  "images": ["https://example.com/refund/evidence1.jpg"]
}
```

## 9. 支付模块

### 9.1 获取支付信息
- **接口**: `GET /orders/{id}/payment`
- **描述**: 获取订单支付信息
- **权限**: 需要登录

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "orderId": 1001,
    "orderNo": "ORD202401010001",
    "amount": 59.80,
    "paymentMethods": [
      {
        "code": "alipay",
        "name": "支付宝",
        "enabled": true
      },
      {
        "code": "wechat",
        "name": "微信支付",
        "enabled": true
      },
      {
        "code": "bank",
        "name": "银行卡",
        "enabled": true
      }
    ],
    "expireTime": "2024-01-01T11:00:00Z"
  }
}
```

### 9.2 支付订单
- **接口**: `POST /orders/{id}/pay`
- **描述**: 发起订单支付
- **权限**: 需要登录

**请求参数**:
```json
{
  "paymentMethod": "alipay",
  "returnUrl": "https://example.com/payment/return",
  "notifyUrl": "https://example.com/payment/notify"
}
```

**响应数据**:
```json
{
  "code": 200,
  "message": "支付发起成功",
  "data": {
    "paymentId": "PAY202401010001",
    "paymentUrl": "https://payment.alipay.com/pay?id=PAY202401010001",
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
  }
}
```

## 10. 物流模块

### 10.1 获取物流信息
- **接口**: `GET /orders/{id}/shipping`
- **描述**: 获取订单物流信息
- **权限**: 需要登录

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "trackingNo": "SF1234567890",
    "carrier": "顺丰速运",
    "carrierCode": "SF",
    "status": "in_transit",
    "statusText": "运输中",
    "timeline": [
      {
        "time": "2024-01-01T14:00:00Z",
        "location": "北京分拣中心",
        "description": "快件已发出"
      },
      {
        "time": "2024-01-01T16:00:00Z",
        "location": "北京转运中心",
        "description": "快件已到达转运中心"
      }
    ],
    "estimatedDelivery": "2024-01-02T18:00:00Z"
  }
}
```

## 11. 订单评价模块

### 11.1 评价订单
- **接口**: `POST /orders/{id}/review`
- **描述**: 对已完成的订单进行评价
- **权限**: 需要登录

**请求参数**:
```json
{
  "items": [
    {
      "productId": 1,
      "rating": 5,
      "content": "苹果很新鲜，包装很好",
      "images": ["https://example.com/reviews/order_review1.jpg"]
    }
  ],
  "serviceRating": 5,
  "serviceComment": "服务很好，发货很快"
}
```

## 12. 管理员模块

### 12.1 数据统计

#### 12.1.1 获取仪表盘统计
- **接口**: `GET /admin/dashboard/stats`
- **描述**: 获取管理后台仪表盘统计数据
- **权限**: 需要管理员权限

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalUsers": 1250,
    "totalOrders": 3680,
    "totalProducts": 156,
    "todaySales": 12345.67,
    "todayOrders": 89,
    "todayUsers": 23,
    "salesTrend": {
      "labels": ["1月", "2月", "3月", "4月", "5月", "6月"],
      "data": [12000, 15000, 18000, 22000, 25000, 28000]
    },
    "categoryStats": [
      {
        "categoryName": "水果",
        "sales": 45.2,
        "count": 1250
      },
      {
        "categoryName": "蔬菜",
        "sales": 32.8,
        "count": 890
      }
    ]
  }
}
```

#### 12.1.2 获取图表数据
- **接口**: `GET /admin/dashboard/charts`
- **描述**: 获取各种图表数据
- **权限**: 需要管理员权限

**查询参数**:
- `type`: 图表类型 (sales, orders, users, products)
- `startDate`: 开始日期
- `endDate`: 结束日期
- `period`: 时间周期 (day, week, month, year)

### 12.2 用户管理

#### 12.2.1 获取用户列表
- **接口**: `GET /admin/users`
- **描述**: 获取所有用户列表
- **权限**: 需要管理员权限

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 20)
- `keyword`: 搜索关键词 (用户名或邮箱)
- `role`: 用户角色 (user, admin)
- `status`: 用户状态 (active, inactive)

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "id": 1,
        "username": "张三",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "avatar": "https://example.com/avatars/user1.jpg",
        "role": "user",
        "status": "active",
        "orderCount": 15,
        "totalSpent": 1250.50,
        "createTime": "2024-01-01T00:00:00Z",
        "lastLoginTime": "2024-01-15T10:30:00Z"
      }
    ],
    "total": 1250,
    "page": 1,
    "pageSize": 20
  }
}
```

#### 12.2.2 获取用户详情
- **接口**: `GET /admin/users/{id}`
- **描述**: 获取指定用户的详细信息
- **权限**: 需要管理员权限

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "user": {
      "id": 1,
      "username": "张三",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "avatar": "https://example.com/avatars/user1.jpg",
      "role": "user",
      "status": "active",
      "createTime": "2024-01-01T00:00:00Z",
      "lastLoginTime": "2024-01-15T10:30:00Z"
    },
    "statistics": {
      "orderCount": 15,
      "totalSpent": 1250.50,
      "favoriteCount": 8,
      "reviewCount": 12
    },
    "recentOrders": [
      {
        "id": 1001,
        "orderNo": "ORD202401010001",
        "status": "completed",
        "amount": 89.90,
        "createTime": "2024-01-15T09:00:00Z"
      }
    ],
    "addresses": [
      {
        "id": 1,
        "name": "张三",
        "phone": "13800138000",
        "address": "北京市朝阳区某某街道123号",
        "isDefault": true
      }
    ]
  }
}
```

#### 12.2.3 更新用户信息
- **接口**: `PUT /admin/users/{id}`
- **描述**: 更新用户信息
- **权限**: 需要管理员权限

**请求参数**:
```json
{
  "username": "李四",
  "email": "<EMAIL>",
  "phone": "13900139000",
  "role": "user",
  "status": "active"
}
```

#### 12.2.4 更新用户状态
- **接口**: `PUT /admin/users/{id}/status`
- **描述**: 启用或禁用用户
- **权限**: 需要管理员权限

**请求参数**:
```json
{
  "status": "inactive",
  "reason": "违规操作"
}
```

### 12.3 商品管理

#### 12.3.1 获取商品列表 (管理员)
- **接口**: `GET /admin/products`
- **描述**: 获取所有商品列表 (包含下架商品)
- **权限**: 需要管理员权限

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 20)
- `keyword`: 搜索关键词
- `categoryId`: 分类ID
- `status`: 商品状态 (active, inactive, draft)

**响应数据**: 同用户端商品列表，但包含更多管理字段

#### 12.3.2 添加商品
- **接口**: `POST /admin/products`
- **描述**: 添加新商品
- **权限**: 需要管理员权限

**请求参数**:
```json
{
  "name": "有机香蕉",
  "description": "新鲜有机香蕉，营养丰富",
  "price": 19.90,
  "originalPrice": 25.90,
  "categoryId": 2,
  "images": [
    "https://example.com/products/banana1.jpg",
    "https://example.com/products/banana2.jpg"
  ],
  "stock": 200,
  "tags": ["有机", "新鲜", "香蕉"],
  "specifications": {
    "产地": "海南",
    "重量": "500g/把",
    "保质期": "5天"
  },
  "status": "active"
}
```

#### 12.3.3 更新商品
- **接口**: `PUT /admin/products/{id}`
- **描述**: 更新商品信息
- **权限**: 需要管理员权限

**请求参数**: 同添加商品

#### 12.3.4 删除商品
- **接口**: `DELETE /admin/products/{id}`
- **描述**: 删除商品 (软删除)
- **权限**: 需要管理员权限

#### 12.3.5 更新商品状态
- **接口**: `PUT /admin/products/{id}/status`
- **描述**: 上架/下架商品
- **权限**: 需要管理员权限

**请求参数**:
```json
{
  "status": "inactive",
  "reason": "库存不足"
}
```

### 12.4 分类管理

#### 12.4.1 添加分类
- **接口**: `POST /admin/categories`
- **描述**: 添加新的商品分类
- **权限**: 需要管理员权限

**请求参数**:
```json
{
  "name": "坚果",
  "parentId": 1,
  "description": "各种坚果类商品",
  "image": "https://example.com/categories/nuts.jpg",
  "sort": 3
}
```

#### 12.4.2 更新分类
- **接口**: `PUT /admin/categories/{id}`
- **描述**: 更新分类信息
- **权限**: 需要管理员权限

#### 12.4.3 删除分类
- **接口**: `DELETE /admin/categories/{id}`
- **描述**: 删除分类 (需要先移除该分类下的所有商品)
- **权限**: 需要管理员权限

### 12.5 订单管理

#### 12.5.1 获取订单列表 (管理员)
- **接口**: `GET /admin/orders`
- **描述**: 获取所有订单列表
- **权限**: 需要管理员权限

**查询参数**:
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 20)
- `status`: 订单状态
- `keyword`: 搜索关键词 (订单号或客户名称)
- `startDate`: 开始日期
- `endDate`: 结束日期

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "items": [
      {
        "id": 1001,
        "orderNo": "ORD202401010001",
        "customerId": 1,
        "customerName": "张三",
        "customerPhone": "13800138000",
        "status": "paid",
        "statusText": "已付款",
        "totalAmount": 59.80,
        "itemCount": 2,
        "paymentMethod": "alipay",
        "createTime": "2024-01-01T09:00:00Z",
        "paidTime": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 3680,
    "page": 1,
    "pageSize": 20
  }
}
```

#### 12.5.2 订单发货
- **接口**: `PUT /admin/orders/{id}/ship`
- **描述**: 为订单安排发货
- **权限**: 需要管理员权限

**请求参数**:
```json
{
  "trackingNo": "SF1234567890",
  "carrier": "顺丰速运",
  "carrierCode": "SF",
  "note": "已安排发货"
}
```

#### 12.5.3 取消订单 (管理员)
- **接口**: `PUT /admin/orders/{id}/cancel`
- **描述**: 管理员取消订单
- **权限**: 需要管理员权限

**请求参数**:
```json
{
  "reason": "商品缺货",
  "refundAmount": 59.80,
  "note": "系统自动退款"
}
```

## 13. 文件上传模块

### 13.1 上传单张图片
- **接口**: `POST /upload/image`
- **描述**: 上传单张图片文件
- **权限**: 需要登录
- **请求格式**: `multipart/form-data`

**请求参数**:
```
image: File (图片文件，支持 jpg, png, gif, webp)
```

**响应数据**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "https://example.com/uploads/images/20240101/abc123.jpg",
    "filename": "abc123.jpg",
    "originalName": "product.jpg",
    "size": 102400,
    "mimeType": "image/jpeg"
  }
}
```

### 13.2 批量上传图片
- **接口**: `POST /upload/images`
- **描述**: 批量上传多张图片
- **权限**: 需要登录
- **请求格式**: `multipart/form-data`

**请求参数**:
```
images: File[] (多个图片文件)
```

**响应数据**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "urls": [
      {
        "url": "https://example.com/uploads/images/20240101/abc123.jpg",
        "filename": "abc123.jpg",
        "originalName": "product1.jpg",
        "size": 102400
      },
      {
        "url": "https://example.com/uploads/images/20240101/def456.jpg",
        "filename": "def456.jpg",
        "originalName": "product2.jpg",
        "size": 98765
      }
    ]
  }
}
```

## 14. 系统配置模块

### 14.1 获取支付方式配置
- **接口**: `GET /config/payment-methods`
- **描述**: 获取系统支持的支付方式
- **权限**: 公开

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "code": "alipay",
      "name": "支付宝",
      "icon": "https://example.com/icons/alipay.png",
      "enabled": true,
      "description": "支持支付宝扫码支付"
    },
    {
      "code": "wechat",
      "name": "微信支付",
      "icon": "https://example.com/icons/wechat.png",
      "enabled": true,
      "description": "支持微信扫码支付"
    },
    {
      "code": "bank",
      "name": "银行卡",
      "icon": "https://example.com/icons/bank.png",
      "enabled": true,
      "description": "支持各大银行卡支付"
    }
  ]
}
```

### 14.2 获取配送配置
- **接口**: `GET /config/shipping`
- **描述**: 获取配送相关配置
- **权限**: 公开

**响应数据**:
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "freeShippingAmount": 99.00,
    "shippingFee": 8.00,
    "regions": [
      {
        "name": "北京市",
        "code": "110000",
        "shippingFee": 8.00,
        "freeShippingAmount": 99.00
      },
      {
        "name": "上海市",
        "code": "310000",
        "shippingFee": 8.00,
        "freeShippingAmount": 99.00
      }
    ],
    "estimatedDeliveryDays": {
      "standard": 3,
      "express": 1
    }
  }
}
```

## 15. 数据模型说明

### 15.1 用户模型 (User)
```json
{
  "id": "integer - 用户ID",
  "username": "string - 用户名",
  "email": "string - 邮箱",
  "phone": "string - 手机号",
  "avatar": "string - 头像URL",
  "role": "enum - 角色 (user|admin)",
  "status": "enum - 状态 (active|inactive)",
  "createTime": "datetime - 创建时间",
  "updateTime": "datetime - 更新时间",
  "lastLoginTime": "datetime - 最后登录时间"
}
```

### 15.2 商品模型 (Product)
```json
{
  "id": "integer - 商品ID",
  "name": "string - 商品名称",
  "description": "text - 商品描述",
  "price": "decimal - 现价",
  "originalPrice": "decimal - 原价",
  "images": "array - 商品图片数组",
  "stock": "integer - 库存数量",
  "categoryId": "integer - 分类ID",
  "tags": "array - 标签数组",
  "sales": "integer - 销量",
  "rating": "decimal - 评分",
  "reviewCount": "integer - 评价数量",
  "specifications": "object - 商品规格",
  "status": "enum - 状态 (active|inactive|draft)",
  "createTime": "datetime - 创建时间",
  "updateTime": "datetime - 更新时间"
}
```

### 15.3 订单模型 (Order)
```json
{
  "id": "integer - 订单ID",
  "orderNo": "string - 订单号",
  "userId": "integer - 用户ID",
  "status": "enum - 订单状态 (pending|paid|shipped|completed|cancelled)",
  "totalAmount": "decimal - 订单总金额",
  "items": "array - 订单商品列表",
  "address": "object - 收货地址",
  "paymentMethod": "string - 支付方式",
  "paymentStatus": "enum - 支付状态",
  "shippingInfo": "object - 物流信息",
  "note": "string - 订单备注",
  "createTime": "datetime - 创建时间",
  "updateTime": "datetime - 更新时间",
  "paidTime": "datetime - 支付时间",
  "shippedTime": "datetime - 发货时间",
  "completedTime": "datetime - 完成时间"
}
```

### 15.4 地址模型 (Address)
```json
{
  "id": "integer - 地址ID",
  "userId": "integer - 用户ID",
  "name": "string - 收货人姓名",
  "phone": "string - 收货人电话",
  "province": "string - 省份",
  "city": "string - 城市",
  "district": "string - 区县",
  "address": "string - 详细地址",
  "isDefault": "boolean - 是否默认地址",
  "createTime": "datetime - 创建时间",
  "updateTime": "datetime - 更新时间"
}
```

## 16. 错误码说明

| 错误码 | 说明 | 示例 |
|--------|------|------|
| 200 | 成功 | 操作成功 |
| 400 | 请求参数错误 | 缺少必填参数 |
| 401 | 未授权 | 登录已过期 |
| 403 | 权限不足 | 需要管理员权限 |
| 404 | 资源不存在 | 商品不存在 |
| 409 | 资源冲突 | 邮箱已被注册 |
| 422 | 参数验证失败 | 邮箱格式不正确 |
| 429 | 请求过于频繁 | 请求限流 |
| 500 | 服务器内部错误 | 系统异常 |

## 17. 接口调用示例

### 17.1 用户登录示例
```bash
curl -X POST "https://api.example.com/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 17.2 获取商品列表示例
```bash
curl -X GET "https://api.example.com/products?page=1&pageSize=20&categoryId=1" \
  -H "Content-Type: application/json"
```

### 17.3 创建订单示例
```bash
curl -X POST "https://api.example.com/orders" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "items": [
      {
        "productId": 1,
        "quantity": 2,
        "price": 29.90
      }
    ],
    "addressId": 1,
    "paymentMethod": "alipay",
    "note": "请尽快发货"
  }'
```

---

**文档版本**: v1.0
**最后更新**: 2024-01-01
**维护者**: 开发团队

> 注意：本文档中的所有示例数据仅供参考，实际开发时请根据具体业务需求进行调整。
